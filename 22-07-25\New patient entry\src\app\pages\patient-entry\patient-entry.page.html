<form [formGroup]="patientForm" (ngSubmit)="savePatients()">
  <div class="popup-overlay">
    <div class="container">
      <div class="form-wrapper">
        <div class="header-container">
          <h2 class="title">Register New Patient</h2>
          <img src="assets/icon/cross.png" alt="" class="close-btn" />
        </div>

        <!--  Progress Steps -->
        <div class="progress-steps">
          <div class="step" [class.active]="currentStep===1" [class.completed]="currentStep>1">
            <span class="dot"></span>
            <div class="label">
              <p class="step-title">Basic Information</p>
              <p class="step-status">{{ currentStep===1 ? 'In Progress' : currentStep>1 ? 'Completed' : 'Pending' }}</p>
            </div>
          </div>

          <div class="step" [class.active]="currentStep===2" [class.completed]="currentStep>2">
            <span class="dot"></span>
            <div class="label">
              <p class="step-title">Additional Information</p>
              <p class="step-status">{{ currentStep===2 ? 'In Progress' : currentStep>2 ? 'Completed' : 'Pending' }}</p>
            </div>
          </div>

          <div class="step" [class.active]="currentStep===3" [class.completed]="currentStep>3">
            <span class="dot"></span>
            <div class="label">
              <p class="step-title">Patient’s Image</p>
              <p class="step-status">{{ currentStep===3 ? 'In Progress' : currentStep>3 ? 'Completed' : 'Pending' }}</p>
            </div>
          </div>

          <div class="step" [class.active]="currentStep===4">
            <span class="dot"></span>
            <div class="label">
              <p class="step-title">Upload Documents</p>
              <p class="step-status">{{ currentStep===4 ? 'In Progress' : 'Pending' }}</p>
            </div>
          </div>
        </div>

        <!--  STEP 1 -->
        <div *ngIf="currentStep===1" class="form-section">
          <h3>Basic Information</h3>
          <div class="form-row">
            <div class="form-group">
              <label>First Name</label>
              <input type="text" formControlName="first_name" placeholder="Enter Patient’s First Name *" required />
            </div>

            <div class="form-group">
              <label>Last Name</label>
              <input type="text" formControlName="last_name" placeholder="Enter Patient’s Last Name *" required />
            </div>

            <div class="form-group">
              <label>Date of Birth</label>
              <input type="date" formControlName="date_of_birth" required />
            </div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label>Gender</label>
              <select formControlName="gender">
                <option disabled disabled>Select Gender</option>
                <option value="Male">Male</option>
                <option value="Female">Female</option>
                <option value="Other">Other</option>
              </select>
            </div>

            <div class="form-group">
              <label>Contact Number</label>
              <input type="text" formControlName="mobile" placeholder="Enter Contact Number *" required />
            </div>

            <div class="form-group">
              <label>Email ID</label>
              <input type="email" formControlName="email" placeholder="Enter Email ID" />
            </div>
          </div>

          <div class="form-row">
            <div class="form-group full-width">
              <label>Address</label>
              <textarea formControlName="address" placeholder="Enter Patient’s Address Here"></textarea>
            </div>
          </div>

          <div class="form-actions">
            <span></span>
            <button type="button" class="btn-next" (click)="nextStep()">Next <img src="assets/icon/next.png" /></button>
          </div>
        </div>

        <!--  STEP 2 -->
        <div *ngIf="currentStep===2" class="form-section">
          <h3>Additional Information</h3>
          <div class="form-row">
            <div class="form-group">
              <label>Relationship Status</label>
              <select formControlName="maritalstatus">
                <option value="" disabled selected>Select a Relationship Status *</option>
                <option value="Single">Single</option>
                <option value="Married">Married</option>
              </select>
            </div>

            <div class="form-group">
              <label>Height</label>
              <input type="number" formControlName="height" placeholder="Enter Patient’s Height (Cms) *" />
            </div>

            <div class="form-group">
              <label>Weight</label>
              <input type="number" formControlName="weight" placeholder="Enter Patient’s Weight (Kgs) *" />
            </div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label>Country</label>
              <select formControlName="country" (change)="onCountryChange($event)">
                <option value="" disabled selected>Select Country *</option>
                <option *ngFor="let country of countryList" [value]="country.CountryId">{{ country.Country }}</option>
              </select>
            </div>

            <div class="form-group">
              <label>State</label>
              <select formControlName="state" (change)="onStateChange($event)">
                <option value="" disabled selected>Select State *</option>
                <option *ngFor="let state of stateList" [value]="state.StateId">{{ state.State }}</option>
              </select>
            </div>

            <div class="form-group">
              <label>District</label>
              <select formControlName="district">
                <option value="" disabled>Select District *</option>
                <option *ngFor="let district of districtList" [value]="district">{{ district }}</option>
              </select>
            </div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label>Block</label>
              <select formControlName="block" (change)="onBlockChange($event)">
                <option value="" disabled selected>Select Block *</option>
                <option *ngFor="let block of blockList" [value]="block.BlockId">{{ block.Block }}</option>
              </select>
            </div>

            <div class="form-group">
              <label>Village</label>
              <select formControlName="village">
                <option value="" disabled selected>Select Village *</option>
                <option *ngFor="let village of villageList" [value]="village.VillageId">{{ village.Village }}</option>
              </select>
            </div>

            <div class="form-group">
              <label>UID</label>
              <input type="text" formControlName="uid" placeholder="Enter UID *" />
            </div>
          </div>

          <div class="form-actions">
            <button type="button" class="btn-back" (click)="prevStep()"><img src="assets/icon/left-arraw.png" /> Back</button>
            <button type="button" class="btn-next" (click)="nextStep()">Next <img src="assets/icon/next.png" /></button>
          </div>
        </div>

        <!--  STEP 3 -->
        <div *ngIf="currentStep===3" class="form-section">
          <h3>Capture Patient’s Image</h3>
          

          <div class="image-container">
            <div class="img-2">
              <!-- Camera/Image Display Box -->
              <div class="image-box" [class.captured]="photoPreviewUrl">
                <!-- Video Stream (when camera is active) -->
                <video #video *ngIf="!photoPreviewUrl && isCameraActive" autoplay muted playsinline></video>

                <!-- Captured Image Preview -->
                <img *ngIf="photoPreviewUrl" [src]="photoPreviewUrl" class="captured-image" />

                <!-- Open Camera Button (when no camera active and no image) -->
                <div *ngIf="!photoPreviewUrl && !isCameraActive" class="open-camera-container">
                  <button type="button" class="btn-open-camera" (click)="openCamera()">
                    <img src="assets/icon/camera.png" />
                    Open Camera
                  </button>
                </div>

                <canvas #canvas hidden></canvas>
              </div>

              <!-- Action Buttons -->
              <div class="actions">
                <!-- Retake Button (only show when image is captured) -->
                <button *ngIf="photoPreviewUrl" type="button" class="btn-retake" (click)="retakeImage()">
                  🔄
                  Retake Image
                </button>

                <!-- Camera Controls (only show when camera is active) -->
                <div *ngIf="isCameraActive && !photoPreviewUrl" class="capture-controls">
                  <div class="btn-camera" (click)="captureImage()">
                    <img src="assets/icon/camera.png" />
                  </div>
                  <div class="btn-dropdown" (click)="toggleCameraDropdown()">
                    <img src="assets/icon/down-arraw.png" />
                  </div>

                  <!-- Camera Selection Dropdown -->
                  <div *ngIf="showCameraDropdown" class="camera-dropdown">
                    <div class="dropdown-header">Select Camera</div>
                    <div *ngFor="let device of videoDevices; let i = index"
                         class="dropdown-item"
                         [class.selected]="selectedCameraId === device.deviceId"
                         (click)="selectCamera(device.deviceId)">
                      <span>{{ device.label || 'Camera ' + (i + 1) }}</span>
                      <span *ngIf="selectedCameraId === device.deviceId" class="check-icon">✓</span>
                    </div>
                  </div>
                </div>

                <!-- Save Button (only show when camera is active) -->
                <button *ngIf="isCameraActive && !photoPreviewUrl" type="button" class="btn-save" (click)="captureImage()">
                  Save
                </button>
              </div>
            </div>
          </div>

          <div class="form-actions">
            <button type="button" class="btn-back" (click)="prevStep()"><img src="assets/icon/left-arraw.png" /> Back</button>
            <button type="button" class="btn-next" (click)="nextStep()">Next <img src="assets/icon/next.png" /></button>
          </div>
        </div>

        <!--  STEP 4 -->
        <div *ngIf="currentStep===4" class="form-section">
          <h3>Upload Documents</h3>

          <div class="upload-docs-wrapper" *ngIf="uploadedDocs.length > 0">
            <!-- Uploaded Documents List (LEFT) -->
            <div class="uploaded-docs">
              <div *ngFor="let doc of uploadedDocs; let i = index" class="uploaded-item"
                   [class.editing-mode]="doc.isEditing"
                   [class.view-mode]="!doc.isEditing">

                <!-- Preview Image -->
                <img class="doc-preview" [src]="doc.preview" />

                <!-- Edit Mode -->
                <div class="doc-info-edit" *ngIf="doc.isEditing">
                  <label class="doc-type-label">Document Type</label>
                  <select [formControl]="getDocTypeControl(i)" class="doc-type-select">
                    <option value="" disabled>Select Document Type</option>
                    <option value="Aadhaar">Aadhaar</option>
                    <option value="PAN">PAN</option>
                    <option value="Passport">Passport</option>
                    <option value="Driving License">Driving License</option>
                    <option value="Voter ID">Voter ID</option>
                    <option value="Other">Other</option>
                  </select>
                </div>

                <!-- View Mode -->
                <div class="doc-info-view" *ngIf="!doc.isEditing">
                  <div class="doc-details-flex">
                    <span class="doc-name">{{ doc.type || 'Unknown' }}</span>
                    <span class="doc-meta">{{ doc.date | date:'dd MMM yyyy' }}</span>
                    <span class="doc-meta">{{ doc.time }}</span>
                  </div>
                </div>

                <!-- Actions -->
                <div class="doc-actions">
                  <!-- Edit Mode Actions -->
                  <ng-container *ngIf="doc.isEditing">
                    <button type="button" class="icon-btn cancel-btn" (click)="cancelEdit(i)">
                      <img src="assets/icon/cross.png" alt="Cancel" />
                    </button>
                    <button type="button" class="icon-btn save-btn" (click)="saveEdit(i)">
                      <img src="assets/icon/right.png" alt="Save" />
                    </button>
                  </ng-container>

                  <!-- View Mode Actions -->
                  <ng-container *ngIf="!doc.isEditing">
                    <button type="button" class="icon-btn edit-btn" (click)="editDoc(i)">
                      <img src="assets/icon/edit.png" alt="Edit" />
                    </button>
                    <button type="button" class="icon-btn delete-btn" (click)="deleteDoc(i)">
                      <img src="assets/icon/delete.png" alt="Delete" />
                    </button>
                  </ng-container>
                </div>
              </div>
            </div>

            <!-- <div class="uploaded-docs">
              <div *ngFor="let doc of uploadedDocs; let i=index" class="uploaded-item">
                <div>
                  <img class="doc-preview" [src]="doc.preview" />
                </div>
                <div class="doc-info">
                  <label for="select">Document Type</label>
                  <select formControlName="type" id="select" class="doc-type">
                    <option value="" disabled selected>Select Document Type</option>
                    <option value="Aadhaar">Aadhaar</option>
                    <option value="PAN">PAN</option>
                    <option value="Passport">Passport</option>
                    <option value="Other">Other</option>
                  </select>
                </div>
                <div class="doc-actions">
                  <button class="icon-btn" (click)="deleteDoc(i)"><img src="assets/icon/cross.png" alt=""></button>
                  <button class="icon-btn" (click)="editDoc(i)"><img src="assets/icon/right.png" alt=""></button>
                </div>
              </div>
            </div> -->

            <!-- Upload Box (RIGHT) -->
            <div class="upload-box" (drop)="onFileDrop($event)" (dragover)="onDragOver($event)">
              <div class="upload-icon">⬆️</div>
              <p class="upload-text">Upload Documents</p>
              <p class="upload-subtext">
                Drag & Drop /
                <span class="choose-file" (click)="fileInput.click()">Choose File</span> /
                <span class="capture-image" (click)="startCapture()">Capture Image</span>
              </p>
              <input type="file" hidden #fileInput (change)="onFileSelected($event)" multiple />
            </div>
          </div>

          <!-- Upload Box (FULL WIDTH) - Only shown when no documents are uploaded -->
          <div class="upload-box-full" *ngIf="uploadedDocs.length === 0" (drop)="onFileDrop($event)" (dragover)="onDragOver($event)">
            <div class="upload-icon"><img src="assets/icon/upload-img.png" /></div>
            <p class="upload-text">Upload Documents</p>
            <div style="display: flex; justify-content: center;">
              <p class="upload-subtext">
                Drag & Drop /
                <span class="choose-file" (click)="fileInput.click()">Choose File</span> /
                <span class="capture-image" (click)="startCapture()">Capture Image</span>
                to upload
              </p>
            </div>
            <input type="file" hidden #fileInput (change)="onFileSelected($event)" multiple />
          </div>

          <!-- Navigation Buttons -->
          <div class="form-actions">
            <button type="button" class="btn-back" (click)="prevStep()"><img src="assets/icon/left-arraw.png" /> Back</button>
            <button type="submit" class="btn-register">Register ✔</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</form>
