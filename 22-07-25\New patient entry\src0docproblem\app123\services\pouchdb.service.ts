import { Injectable } from '@angular/core';
import { Observable, from, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import PouchDB from 'pouchdb-browser';
import PouchDBFind from 'pouchdb-find';
import { generateUUIDv4 } from '../shared/utils';


@Injectable({ providedIn: 'root' })
export class PouchdbService {
  private db!: PouchDB.Database;
  private remoteDB!: PouchDB.Database; // Optional: For syncing with remote CouchDB server

  constructor() {
    PouchDB.plugin(PouchDBFind);
    // Initialize the database automatically with consultation data
    this.initDB('register_patient');
   
  }
  /**
   *  Initialize the database with a dynamic name
   * Example: this.initDB('users') OR this.initDB('products')
   */
  initDB(dbName: string) {
    this.db = new PouchDB(dbName, { adapter: 'idb' }); // IndexedDB adapter for browsers
    console.log(` PouchDB Initialized: ${dbName}`);
  }

   //  Add a new record
   
  addRecord(data: any): Observable<any> {
    data._id = generateUUIDv4(); // Generate a unique ID
    return from(this.db.put(data)).pipe(map(res => res), catchError(this.handleError));
  }

   //  Get all records
   
  getAllRecords<T>(): Observable<T[]> {
    return from(this.db.allDocs({ include_docs: true })).pipe(
      map(res => res.rows.map(r => r.doc as T)),
      catchError(this.handleError)
    );
  }

  // Get record by ID
  
  getRecordById<T>(id: string): Observable<T> {
    return from(this.db.get<T>(id)).pipe(map(res => res), catchError(this.handleError));
  }

   // Update record (must have _id & _rev)
  
  updateRecord(data: any): Observable<any> {
    if (!data._id || !data._rev) {
      return throwError(() => new Error(' Record must have _id and _rev to update'));
    }
    return from(this.db.put(data)).pipe(map(res => res), catchError(this.handleError));
  }

  //  Delete record (must have _id & _rev)
  
  deleteRecord(data: any): Observable<any> {
    if (!data._id || !data._rev) {
      return throwError(() => new Error(' Record must have _id and _rev to delete'));
    }
    return from(this.db.remove(data._id, data._rev)).pipe(map(res => res), catchError(this.handleError));
  }

  // 🔹================ MASTER DATA FUNCTIONS =================

  /**  Add/Update Master Data (Global static JSON) */
  addOrUpdateMasterData(data: any): Observable<any> {
    const docId = 'master_data';
    return from(
      this.db.get(docId).then(doc => {
        return this.db.put({ ...doc, ...data, _id: docId, _rev: doc._rev });
      }).catch(() => {
        return this.db.put({ _id: docId, ...data });
      })
    ).pipe(map(res => res), catchError(this.handleError));
  }

  /**  Fetch entire Master Data */
  getMasterData(): Observable<any> {
    return from(this.db.get('master_data')).pipe(map(res => res), catchError(this.handleError));
  }

  /**  Fetch specific table from Master Data */
  getMasterTable(tableName: string): Observable<any[]> {
    return from(this.db.get('master_data')).pipe(
      map((res: any) => res[tableName] || []),
      catchError(this.handleError)
    );
  }

  // 🔹================ CONSULTATION DATA FUNCTIONS =================

  /**  Get records by type (for consultation data like complaints, diagnosis, etc.) */
  getRecordsByType<T>(type: string): Observable<T[]> {
    return from(this.db.allDocs({ include_docs: true })).pipe(
      map(res => res.rows
        .map(r => r.doc as T)
        .filter((doc: any) => doc.type === type)
      ),
      catchError(this.handleError)
    );
  }

  //  Sync local DB with remote CouchDB/PouchDB server
  // syncWithServer(): Observable<any> {
  //   return new Observable(observer => {
  //     if (!this.remoteDB) {
  //       observer.error(' Remote DB not configured!');
  //       return;
  //     }
  //     const syncHandler = this.db.sync(this.remoteDB, { live: true, retry: true })
  //       .on('change', info => observer.next(info))
  //       .on('paused', err => console.log(' Sync paused:', err))
  //       .on('active', () => console.log(' Sync active'))
  //       .on('error', err => observer.error(err));

  //     return () => syncHandler.cancel(); // Cleanup on unsubscribe
  //   });
  // }

  //  Common Error Handler
  private handleError(error: any) {
    console.error(' PouchDB Error:', error);
    return throwError(() => error);
  }
}



