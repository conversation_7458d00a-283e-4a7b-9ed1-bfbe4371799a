# Complete API Sync Documentation for PouchDB + Backend Integration

## 📋 Overview

This documentation provides a complete implementation guide for syncing PouchDB data with backend APIs. The system ensures offline-first functionality with automatic synchronization when the device comes online.

## 🎯 Key Features

- **Offline-First**: Data saved locally in PouchDB when offline
- **Auto-Sync**: Automatic synchronization when network is available
- **Retry Mechanism**: Failed syncs automatically retry
- **No Duplicates**: Synced records are flagged to prevent re-syncing
- **Network Awareness**: Detects online/offline status changes
- **Error Handling**: Comprehensive error handling with user feedback

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   User Input    │───▶│    PouchDB      │───▶│  Backend API    │
│                 │    │  (Local Store)  │    │   (Server)      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │                        │
                              ▼                        ▼
                       ┌─────────────────┐    ┌─────────────────┐
                       │ isSync: false   │    │ isSync: true    │
                       │ (Unsynced)      │    │ (Synced)        │
                       └─────────────────┘    └─────────────────┘
```

## 🔧 Implementation Guide

### Step 1: Data Structure Setup

Ensure your data model includes the sync flag:

```typescript
interface PatientData {
  _id?: string;
  _rev?: string;
  // ... your existing fields
  isSync: boolean;  // Sync status flag
  type: string;     // Record type for filtering
}
```

### Step 2: Core Sync Methods

#### 2.1 Individual Record Sync

```typescript
/**
 * Sync individual record with backend API
 * @param recordData - The record to sync
 */
private async syncWithBackend(recordData: any) {
  try {
    console.log('🚀 Auto-syncing record with backend API...');

    // Prepare data for backend (remove PouchDB-specific fields)
    const backendPayload: any = { ...recordData };
    
    // Remove PouchDB specific fields
    delete backendPayload._id;
    delete backendPayload._rev;
    delete backendPayload.type;
    delete backendPayload.isSync;

    // Apply any backend-specific transformations
    this.transformDataForBackend(backendPayload);

    // Build query string for API call
    const query = this.buildApiQuery(backendPayload);

    console.log('📤 Sending to backend:', backendPayload);

    // Call backend API
    const response = await lastValueFrom(
      this.http.post(`${this.apiEndpoint}${query}`, backendPayload)
    );

    console.log('✅ Backend sync successful:', response);

    // Update local record with sync flag
    const updatedRecord = { ...recordData };
    updatedRecord.isSync = true;

    // Update backend response data if available
    if (response && (response as any).data) {
      this.updateWithBackendResponse(updatedRecord, (response as any).data);
    }

    // Update in PouchDB with sync flag
    await lastValueFrom(this.pouchdbService.updateRecord(updatedRecord));

    this.showSuccessMessage('✅ Record synced with backend successfully!');

  } catch (error) {
    console.error('❌ Backend sync failed:', error);
    this.showWarningMessage('⚠️ Record saved locally. Backend sync will retry later.');
  }
}
```

#### 2.2 Batch Sync for Unsynced Records

```typescript
/**
 * Sync all unsynced records with backend API
 */
private async syncAllUnsyncedRecords() {
  try {
    this.pouchdbService.getAllRecords<any>().subscribe({
      next: (records) => {
        const unsyncedRecords = records.filter(record => 
          record.type === 'your_record_type' && !record.isSync
        );

        if (unsyncedRecords.length === 0) {
          console.log('✅ All records are already synced');
          return;
        }

        console.log(`🔄 Found ${unsyncedRecords.length} unsynced records. Starting sync...`);

        // Process each unsynced record with delay
        unsyncedRecords.forEach(async (record, index) => {
          try {
            // Add delay between syncs to avoid overwhelming backend
            await new Promise(resolve => setTimeout(resolve, index * 500));
            
            await this.syncWithBackend(record);
            console.log(`✅ Record ${index + 1}/${unsyncedRecords.length} synced successfully`);
            
          } catch (error) {
            console.error(`❌ Failed to sync record ${record._id}:`, error);
            // Continue with next record even if one fails
          }
        });
      },
      error: (err) => {
        console.error('❌ Error fetching unsynced records:', err);
      }
    });
  } catch (error) {
    console.error('❌ Error in syncAllUnsyncedRecords:', error);
  }
}
```

#### 2.3 Network Monitoring and Auto-Sync

```typescript
/**
 * Check network connectivity and auto-sync when online
 */
private checkNetworkAndSync() {
  if (navigator.onLine) {
    console.log('🌐 Device is online - Starting auto-sync...');
    this.syncAllUnsyncedRecords();
  } else {
    console.log('📴 Device is offline - Sync will retry when online');
  }

  // Listen for network status changes
  window.addEventListener('online', () => {
    console.log('🌐 Network restored - Starting auto-sync...');
    this.syncAllUnsyncedRecords();
  });

  window.addEventListener('offline', () => {
    console.log('📴 Network lost - Data will be saved locally');
  });
}
```

### Step 3: Helper Methods

#### 3.1 Data Transformation for Backend

```typescript
/**
 * Transform data for backend compatibility
 */
private transformDataForBackend(data: any) {
  // Convert string values to proper types
  if (data.gender) data.gender = data.gender.toLowerCase();
  if (data.maritalstatus) data.maritalstatus = data.maritalstatus.toLowerCase();
  
  // Convert location fields to numbers
  data.country = parseInt(data.country) || 0;
  data.state = parseInt(data.state) || 0;
  data.district = parseInt(data.district) || 0;
  
  // Replace "null" strings with real null
  Object.keys(data).forEach(key => {
    if (typeof data[key] === 'string') {
      data[key] = this.sanitizeValue(data[key]);
    }
  });

  // Remove base64 data from documents for backend
  if (data.documents && Array.isArray(data.documents)) {
    data.documents = data.documents.map((doc: any) => ({
      id: doc.id || 0,
      patientid: doc.patientid || data.patientid || 0,
      imagepath: doc.imagepath || '',
      S3URL: doc.S3URL || '',
      fileName: doc.fileName || '',
      fileType: doc.fileType || '',
      type: doc.type || ''
      // Remove 'data' field for backend
    }));
  }
}

/**
 * Sanitize string values
 */
private sanitizeValue(value: any): any {
  if (value === 'null' || value === 'NULL') return null;
  if (value === '' || value === undefined) return value === '' ? '' : null;
  return value;
}
```

#### 3.2 API Query Builder

```typescript
/**
 * Build API query string
 */
private buildApiQuery(data: any): string {
  return `?action=addRecord&recordData=${encodeURIComponent(JSON.stringify(data))}&domain=${data.domain || 0}&token=${this.getAuthToken()}`;
}

/**
 * Get authentication token
 */
private getAuthToken(): string {
  // Implement your token retrieval logic
  return 'your_auth_token';
}
```

#### 3.3 Backend Response Handler

```typescript
/**
 * Update record with backend response data
 */
private updateWithBackendResponse(record: any, backendData: any) {
  // Update profile S3URL if provided by backend
  if (backendData.profile?.S3URL) {
    record.profile.S3URL = backendData.profile.S3URL;
  }

  // Update documents S3URLs if provided by backend
  if (backendData.documents && Array.isArray(backendData.documents)) {
    backendData.documents.forEach((backendDoc: any, index: number) => {
      if (record.documents[index] && backendDoc.S3URL) {
        record.documents[index].S3URL = backendDoc.S3URL;
      }
    });
  }
}
```

### Step 4: Integration in Component Lifecycle

```typescript
async ngOnInit() {
  // ... your existing initialization code

  // Initialize network monitoring and auto-sync
  this.checkNetworkAndSync();

  // ... rest of your initialization
}

async saveRecord() {
  try {
    // Prepare record data
    const recordData = this.prepareRecordData();
    
    // Set initial sync status
    recordData.isSync = false;
    recordData.type = 'your_record_type';

    // Save to PouchDB first
    const savedRecord = await lastValueFrom(this.pouchdbService.addRecord(recordData));

    // Auto-sync with backend if online
    if (!savedRecord.isSync) {
      this.syncWithBackend(savedRecord);
    }

    this.showSuccessMessage('Record saved successfully. Syncing with backend...');
    
  } catch (error) {
    console.error('❌ Failed to save record:', error);
    this.showErrorMessage('Failed to save record.');
  }
}
```

## 🔧 Configuration

### API Endpoint Configuration

```typescript
// Update these constants with your actual API details
private readonly apiEndpoint = 'https://your-backend-api.com/api/records';
private readonly authToken = 'your_auth_token';
private readonly recordType = 'your_record_type';
```

### PouchDB Service Integration

Ensure your PouchDB service has these methods:
- `getAllRecords<T>(): Observable<T[]>`
- `addRecord(data: any): Observable<any>`
- `updateRecord(data: any): Observable<any>`
- `getRecordById<T>(id: string): Observable<T>`

## 🧪 Testing Guide

### Test Scenarios

1. **Offline Mode Test**
   ```
   1. Disconnect internet
   2. Add new records
   3. Verify records saved locally with isSync: false
   4. Reconnect internet
   5. Verify auto-sync occurs
   ```

2. **Sync Retry Test**
   ```
   1. Simulate backend API failure
   2. Verify records remain unsynced
   3. Fix backend/network
   4. Verify retry mechanism works
   ```

3. **Network Change Test**
   ```
   1. Monitor console for network events
   2. Toggle airplane mode
   3. Verify sync triggers on network restore
   ```

## 🚨 Error Handling

The system handles these error scenarios:
- Network connectivity issues
- Backend API failures
- Invalid data formats
- Authentication failures
- Timeout errors

## 📊 Monitoring and Logging

All sync operations are logged with emojis for easy identification:
- 🚀 Sync start
- ✅ Sync success
- ❌ Sync failure
- 🔄 Batch sync
- 🌐 Network online
- 📴 Network offline

## 🔒 Security Considerations

- Always validate data before sending to backend
- Use proper authentication tokens
- Sanitize user input
- Handle sensitive data appropriately
- Implement rate limiting for API calls

## 📈 Performance Optimization

- Batch processing with delays
- Efficient filtering of unsynced records
- Minimal data transformation
- Proper error recovery
- Memory-efficient operations

This implementation provides a robust, production-ready sync solution that can be adapted for any PouchDB + Backend API integration.

## 🔄 Complete Implementation Example

### Full Service Integration

```typescript
import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { ToastController } from '@ionic/angular';
import { Observable, lastValueFrom } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class SyncService {
  private readonly API_ENDPOINT = 'https://your-api.com/api';
  private readonly AUTH_TOKEN = 'your_auth_token';

  constructor(
    private http: HttpClient,
    private pouchdbService: PouchdbService,
    private toastController: ToastController
  ) {}

  /**
   * Universal sync method for any record type
   */
  async syncRecord(recordData: any, recordType: string, apiAction: string) {
    try {
      const backendPayload = this.prepareForBackend(recordData);
      const query = this.buildQuery(backendPayload, apiAction);

      const response = await lastValueFrom(
        this.http.post(`${this.API_ENDPOINT}/${recordType}${query}`, backendPayload)
      );

      await this.markAsSynced(recordData);
      this.showSuccess(`${recordType} synced successfully!`);

      return response;
    } catch (error) {
      this.handleSyncError(error, recordType);
      throw error;
    }
  }

  /**
   * Batch sync for multiple records
   */
  async batchSync(recordType: string, apiAction: string) {
    const records = await this.getUnsyncedRecords(recordType);

    for (let i = 0; i < records.length; i++) {
      try {
        await new Promise(resolve => setTimeout(resolve, i * 500));
        await this.syncRecord(records[i], recordType, apiAction);
      } catch (error) {
        console.error(`Failed to sync ${recordType} ${records[i]._id}:`, error);
      }
    }
  }

  private prepareForBackend(data: any): any {
    const payload = { ...data };
    delete payload._id;
    delete payload._rev;
    delete payload.type;
    delete payload.isSync;
    return payload;
  }

  private buildQuery(data: any, action: string): string {
    return `?action=${action}&data=${encodeURIComponent(JSON.stringify(data))}&token=${this.AUTH_TOKEN}`;
  }

  private async markAsSynced(record: any): Promise<void> {
    record.isSync = true;
    await lastValueFrom(this.pouchdbService.updateRecord(record));
  }

  private async getUnsyncedRecords(type: string): Promise<any[]> {
    const allRecords = await lastValueFrom(this.pouchdbService.getAllRecords());
    return allRecords.filter(record => record.type === type && !record.isSync);
  }

  private async showSuccess(message: string): Promise<void> {
    const toast = await this.toastController.create({
      message,
      duration: 2000,
      color: 'success'
    });
    await toast.present();
  }

  private handleSyncError(error: any, recordType: string): void {
    console.error(`❌ ${recordType} sync failed:`, error);
    // Implement your error handling logic
  }
}
```

### Component Integration Pattern

```typescript
export class DataEntryComponent implements OnInit {

  constructor(
    private syncService: SyncService,
    private pouchdbService: PouchdbService
  ) {}

  async ngOnInit() {
    // Initialize auto-sync
    this.initializeAutoSync();
  }

  async saveData() {
    try {
      // Save locally first
      const data = this.prepareData();
      data.isSync = false;
      data.type = 'your_record_type';

      const savedRecord = await lastValueFrom(
        this.pouchdbService.addRecord(data)
      );

      // Attempt immediate sync if online
      if (navigator.onLine) {
        try {
          await this.syncService.syncRecord(savedRecord, 'records', 'addRecord');
        } catch (error) {
          // Sync will retry later automatically
          console.log('Immediate sync failed, will retry later');
        }
      }

      this.showSuccess('Data saved successfully!');

    } catch (error) {
      this.showError('Failed to save data');
    }
  }

  private initializeAutoSync(): void {
    // Initial sync check
    if (navigator.onLine) {
      this.syncService.batchSync('your_record_type', 'addRecord');
    }

    // Network event listeners
    window.addEventListener('online', () => {
      this.syncService.batchSync('your_record_type', 'addRecord');
    });
  }
}
```

## 🛡️ Advanced Error Handling

### Retry Mechanism with Exponential Backoff

```typescript
class AdvancedSyncService {
  private maxRetries = 3;
  private baseDelay = 1000; // 1 second

  async syncWithRetry(record: any, attempt: number = 1): Promise<any> {
    try {
      return await this.syncRecord(record, 'records', 'addRecord');
    } catch (error) {
      if (attempt < this.maxRetries) {
        const delay = this.baseDelay * Math.pow(2, attempt - 1);
        console.log(`Retry ${attempt}/${this.maxRetries} in ${delay}ms`);

        await new Promise(resolve => setTimeout(resolve, delay));
        return this.syncWithRetry(record, attempt + 1);
      }

      throw new Error(`Sync failed after ${this.maxRetries} attempts`);
    }
  }
}
```

### Network Quality Detection

```typescript
class NetworkAwareSync {
  private isSlowConnection(): boolean {
    const connection = (navigator as any).connection;
    if (!connection) return false;

    return connection.effectiveType === 'slow-2g' ||
           connection.effectiveType === '2g';
  }

  async smartSync(): Promise<void> {
    if (this.isSlowConnection()) {
      // Reduce batch size for slow connections
      await this.syncInSmallBatches();
    } else {
      // Normal batch sync
      await this.normalBatchSync();
    }
  }
}
```

## 📱 Mobile-Specific Considerations

### Background Sync Support

```typescript
// Register service worker for background sync
if ('serviceWorker' in navigator && 'sync' in window.ServiceWorkerRegistration.prototype) {
  navigator.serviceWorker.register('/sw.js').then(registration => {
    return registration.sync.register('background-sync');
  });
}

// Service Worker (sw.js)
self.addEventListener('sync', event => {
  if (event.tag === 'background-sync') {
    event.waitUntil(performBackgroundSync());
  }
});
```

### Battery-Aware Syncing

```typescript
class BatteryAwareSync {
  private async isBatteryLow(): Promise<boolean> {
    if ('getBattery' in navigator) {
      const battery = await (navigator as any).getBattery();
      return battery.level < 0.2; // Less than 20%
    }
    return false;
  }

  async conditionalSync(): Promise<void> {
    const lowBattery = await this.isBatteryLow();

    if (lowBattery) {
      console.log('Battery low, deferring sync');
      return;
    }

    await this.performSync();
  }
}
```

## 🔍 Monitoring and Analytics

### Sync Performance Tracking

```typescript
class SyncAnalytics {
  private trackSyncPerformance(startTime: number, recordCount: number, success: boolean): void {
    const duration = Date.now() - startTime;

    const metrics = {
      duration,
      recordCount,
      success,
      timestamp: new Date().toISOString(),
      networkType: this.getNetworkType()
    };

    // Send to analytics service
    this.sendAnalytics('sync_performance', metrics);
  }

  private getNetworkType(): string {
    const connection = (navigator as any).connection;
    return connection ? connection.effectiveType : 'unknown';
  }
}
```

### Health Check System

```typescript
class SyncHealthMonitor {
  async performHealthCheck(): Promise<boolean> {
    try {
      // Check API availability
      const response = await fetch(`${this.API_ENDPOINT}/health`);

      if (!response.ok) {
        throw new Error(`API health check failed: ${response.status}`);
      }

      // Check local database
      await this.pouchdbService.getAllRecords();

      return true;
    } catch (error) {
      console.error('Health check failed:', error);
      return false;
    }
  }
}
```

## 🚀 Production Deployment Checklist

- [ ] Configure production API endpoints
- [ ] Set up proper authentication tokens
- [ ] Implement rate limiting
- [ ] Add comprehensive logging
- [ ] Set up monitoring and alerts
- [ ] Test offline/online scenarios
- [ ] Verify data integrity
- [ ] Performance test with large datasets
- [ ] Security audit of data transmission
- [ ] Backup and recovery procedures

This comprehensive guide provides everything needed to implement robust, production-ready API sync functionality.
