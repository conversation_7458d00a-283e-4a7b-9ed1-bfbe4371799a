export class mPatientData {
  _id?: string;
  _rev?: string;
  domainwisepid: number = 0
  patientid: number = 0
  first_name: string = ''
  last_name: string = ''
  date_of_birth: string = ''
  age: string = ''
  ageYears: string = ''
  gender: string = ''
  maritalstatus: string = ''
  height: string = ''
  weight: string = ''
  mobile: string = ''
  email: string = ''
  head_of_household_fname: string = ''
  head_of_household_lname: string = ''
  country: number = 0
  state: number = 0
  district: number = 0
  block: number = 0
  village: number = 0
  address: string = ''
  projid: string = ''
  head_of_household_mobile: string = ''
  isAbhaPatient: boolean = false
  profile: Profile = new Profile()
  documents: Document[] = [];
  pastrecord: any
  createdat: string = ''
  createdby: string = ''
  domain: number = 0
  uid: string = ''
  prefix: any
  EhealthId: string = ''
  MRN: string = ''
  password: string = ''
  consentformcheckstatus: number = 0
  fingerPrintTemplate: string = ''
  health_number: string = ''
  health_address: string = ''
  unique_id: any
  nationalId: any
  ethnicity: any
  subscriptionDetails: SubscriptionDetails = new SubscriptionDetails()
  localId: string = ''
  patient_status: any
  patient_title: any
  postCode: any
  centerName: any
  status: any
  type: string = 'patient';
  isSync:boolean = false
  
}

export class Profile {
  id: number = 0
  patientid: number = 0
  imagepath: string = ''  // Path for patient profile image (e.g., "patientdata/images/profilepic/19.0_pat_6856")
  S3URL: string = ''      // S3 URL for patient profile image (populated by backend)
}

export class Document {
  id: number = 0
  patientid: number = 0
  imagepath: string = ''  // Path for document image (e.g., "patientdata/documents/doc_123")
  S3URL: string = ''      // S3 URL for document (populated by backend)
  fileName: string = ''   // Original file name
  fileType: string = ''   // MIME type (e.g., "image/png", "application/pdf")
  data: any = null        // Base64 encoded file data (for local storage)
  type: string = ''       // Document type (e.g., "Aadhaar", "PAN", "Passport")
}

export class SubscriptionDetails {
  subscribedId: number = 0
  familycardid: any
  freeSubcriptionAllocated: number = 0
  completedFreeSubcrition: number = 0
  remainingSubcription: number = 0
  isActive: any
  subcriptionName: any
  subscriptionPlanActivatedOn: any
  subscriptionExpiredOn: any
  isExpaired: number = 0
}

