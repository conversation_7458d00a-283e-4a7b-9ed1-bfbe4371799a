@import url('https://fonts.googleapis.com/css2?family=DM+Sans:wght@400;500;700&display=swap');

/* ---------- Popup Overlay ---------- */
.popup-overlay {
  position: fixed;
  top: 0; left: 0;
  width: 100%; height: 100%;
  background: rgba(0, 0, 0, 0.4);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

/* ---------- Container ---------- */

.container {
  width: 94%;
  height: 90vh;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
  display: flex;
  flex-direction: column;
}

.header-container{
  display: flex;
  justify-content: space-between;
  padding: 16px;
  border-bottom:1px solid #E5E7EB;
  background: #fff;
  position: sticky;
  top: 0;
  z-index: 100;
}

.title{
  font-family: 'DM Sans', sans-serif;
  font-weight: 500;
  font-size: 20px;
  line-height: 130%;
  letter-spacing: 0;
  vertical-align: middle;
  color: #111827;
  margin: 0;
}

.close-btn{
  height: 15px;
  width: 15px;
}

.form-wrapper {
  width: 100%;
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 32px;
}

/* ---------- Progress Steps ---------- */
.progress-steps {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 24px 32px 32px 32px;
  border-bottom: 1px solid #E5E7EB;
  background: #fff;
  position: sticky;
  top: 0;
  z-index: 100;
  margin-top: 16px;
  min-height: 80px;
  width: 61%;
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  opacity: 0.5;
  position: relative;
  z-index: 2;
  flex: 1;
  min-width: 0;
  margin-right: 20px;
}

.step:not(:last-child)::after {
  content: "";
  position: absolute;
  top: 7px;
  left: calc(50% + 7px);
  width: calc(100% - 14px);
  height: 2px;
  background: #E5E7EB;
  z-index: 1;
}

.step.active {
  opacity: 1;
}

.step.active:not(:last-child)::after {
  background: #F59E0B;
}

.step.completed {
  opacity: 1;
}

.step.completed .dot {
  background: #10B981;
  display: flex;
  align-items: center;
  justify-content: center;
}

.step.completed .dot::after {
  content: "✓";
  color: white;
  font-size: 9px;
  font-weight: bold;
  line-height: 1;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.step.completed:not(:last-child)::after {
  background: #10B981;
}

.step.completed .label .step-status {
  color: #10B981;
}

.step.active .dot {
  background: #F59E0B;
}

.step .dot {
  width: 14px;
  height: 14px;
  border-radius: 50%;
  background: #E5E7EB;
  position: relative;
  z-index: 3;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 25px;
}

.label {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.label .step-title {
  font-family: 'DM Sans', sans-serif;
  font-weight: 600;
  font-size: 14px;
  line-height: 140%;
  letter-spacing: 0.4px;
  color: #111827;
  text-align: center;
  margin: 0;
  white-space: nowrap;
}

.label .step-status {
  font-family: 'DM Sans', sans-serif;
  font-weight: 400;
  font-size: 11px;
  line-height: 140%;
  letter-spacing: 0.2px;
  color: #6B7280;
  text-align: center;
  margin: 0;
  white-space: nowrap;
}

/* Status color variations */
.step.completed .label .step-status {
  color: #10B981;
}

.step.active .label .step-status {
  color: #F59E0B;
}

/* ---------- Form Layout ---------- */

input[type="date"] {
  font-size: 14px;
  color: #374151;
}
input::placeholder {
  font-size: 14px;
  color: #374151;
}
select option[disabled] {
  color:#374151;
  font-size: 14px;
}
select {
  font-size: 14px;
   color:#374151;
}
.form-section {
    margin-top: 24px;
    /* padding: 0 32px 32px 32px; */
    overflow-y: auto;
    flex: 1;
    -ms-overflow-style: none;
    scrollbar-width: none;

  /* Hide scrollbar for Chrome, Safari and Opera */
  &::-webkit-scrollbar {
    display: none;
  }

  /* Hide scrollbar for Firefox */
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

.form-section h3 {
  font-family: 'DM Sans', sans-serif;
font-weight: 400; /* Regular weight */
font-size: 16px;
line-height: 150%; /* or 1.5 */
letter-spacing: 0;
vertical-align: middle;
color: #4A4A48;
padding: 8px 16px;
}

.form-row {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.form-group {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 6px;
  min-width: 200px;
}

.form-group.full-width {
  flex: 1 1 100%;
}

label {
font-family: 'DM Sans', sans-serif;
font-weight: 500;
font-size: 13px;
line-height: 140%;
letter-spacing: 0.2px;
color: #374151;
    margin-top: 29px;
    margin-bottom: 11px;
    margin-left:6px;
}

input, select {
width: 100%;
height: 48px;
border-radius: 8px;
gap: 10px;
opacity: 1;
padding: 12px 16px;
border-width: 1px;
border-style: solid;
border-color: #D1D5DB;

}
textarea{
 width: 100%;
   min-width: 320px;
height: 96px;
min-height: 96px;
max-height: 160px;
border-radius: 8px;
gap: 10px;
opacity: 1;
padding: 12px 16px;
border-width: 1px;
border-style: solid;
border-color: #D1D5DB;
}

input:focus, select:focus {
  border-color: #999;
}

/* ---------- Form Actions ---------- */
.form-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 33px;
  border-top: 1px solid #E5E7EB;
  padding: 20px;
  gap: 16px;
}

.btn-back {
  background: none;
  border: none;
  color: #007AFF;
  font-size: 14px;
  cursor: pointer;
  min-width: 80px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-family: 'DM Sans', system-ui, -apple-system, sans-serif;
  font-weight: 600;
  font-size: 14px;
  line-height: 140%;
  letter-spacing: 0.4px;
  text-align: center;
  padding: 12px 16px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.btn-back:hover {
  background: #F0F8FF;
}

.btn-back img {
  width: 14px;
  height: 14px;
}

.btn-next {
  min-width: 120px;
  height: 48px;
  border-radius: 8px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  opacity: 1;
  padding: 12px 24px;
  border: 1px solid #007AFF;
  background-color: transparent;
  color: #007AFF;
  box-sizing: border-box;
  font-family: 'DM Sans', sans-serif;
  font-weight: 600;
  font-size: 14px;
  line-height: 140%;
  letter-spacing: 0.4px;
  cursor: pointer;
  transition: all 0.3s ease;
}



// .btn-next:hover {
//   background: #0056CC;
// }

.btn-register {
  background: #007AFF;
  color: #fff;
  padding: 12px 24px;
  font-size: 14px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  min-width: 120px;
  height: 48px;
  font-family: 'DM Sans', sans-serif;
  font-weight: 600;
  transition: all 0.3s ease;
}

.btn-register:hover {
  background: #0056CC;
}

/* ---------- Image Capture Section ---------- */
.image-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  margin-top: 20px;
}

.img-2{
  width: 402.67px; /* rounded for readability */
  height: 366px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  opacity: 1;
}

.image-box {
  width: 402.67px;   /* rounded for simplicity */
  height: 302px;
  border-radius: 8px;
  opacity: 1;
  background-color: #D1D5DB;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.image-box video {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
}

.image-box .captured-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
}

.open-camera-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.btn-open-camera {
  width: 160px;
  height: 48px;
  border-radius: 8px;
  border: 1px solid #007AFF;
  background: transparent;
  color: #007AFF;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-family: 'DM Sans', sans-serif;
  font-weight: 600;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-open-camera:hover {
  background: #007AFF;
  color: white;
}

.btn-open-camera img {
  width: 20px;
  height: 20px;
}

.actions {
  display: flex;
  gap: 20px;
  align-items: center;
  justify-content: space-between;
}

.btn-retake {
  width: 140px;
  min-width: 140px;
  height: 48px;
  min-height: 48px;
  border-radius: 8px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 24px;
  box-sizing: border-box;
  border: 1px solid #007AFF;
  opacity: 1;
  background: transparent;
  color: #007AFF;
  font-family: 'DM Sans', sans-serif;
  font-weight: 600;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-retake:hover {
  background: #007AFF;
  color: white;
}



.btn-save {
  width: 120px;
  min-width: 120px;
  height: 48px;
  min-height: 48px;
  border-radius: 8px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 24px;
  box-sizing: border-box;
  border: 1px solid #007AFF;
  opacity: 1;
  background: #007AFF;
  color: white;
  font-family: 'DM Sans', sans-serif;
  font-weight: 600;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-save:hover {
  background: #0056CC;
}

.capture-controls {
  display: flex;
  gap: 0;
  width: 96px;
  height: 48px;
  position: relative;
}

.btn-camera {
  width: 48px;
  height: 48px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  border-top-left-radius: 8px;
  border-bottom-left-radius: 8px;
  opacity: 1;
  cursor: pointer;
  background-color: #007AFF;
  border: 1px solid #007AFF;
  transition: all 0.3s ease;
}

.btn-camera:hover {
  background-color: #0056CC;
}

.btn-dropdown {
  width: 48px;
  height: 48px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  border-top-right-radius: 8px;
  border-bottom-right-radius: 8px;
  border: 1px solid #007AFF;
  opacity: 1;
  background: transparent;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-dropdown:hover {
  background: #F0F8FF;
}

.btn-camera img,
.btn-dropdown img {
  max-width: 24px;
  max-height: 24px;
}

/* Camera Dropdown Styles */
.camera-dropdown {
  position: absolute;
  top: 52px;
  right: 0;
  width: 250px;
  background: white;
  border: 1px solid #E5E7EB;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  overflow: hidden;
}

.dropdown-header {
  padding: 12px 16px;
  background: #F9FAFB;
  border-bottom: 1px solid #E5E7EB;
  font-family: 'DM Sans', sans-serif;
  font-weight: 600;
  font-size: 14px;
  color: #374151;
}

.dropdown-item {
  padding: 12px 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  font-family: 'DM Sans', sans-serif;
  font-size: 14px;
  color: #374151;
  transition: background 0.2s ease;
}

.dropdown-item:hover {
  background: #F3F4F6;
}

.dropdown-item.selected {
  background: #EBF8FF;
  color: #007AFF;
}

.check-icon {
  color: #007AFF;
  font-weight: bold;
  font-size: 16px;
}

/* ---------- Upload Section ---------- */
.upload-box, .upload-box-full {
    display: flex;
    border: 1px dashed #c7c7c7;
    border-radius: 6px;
    padding: 40px;
    text-align: center;
    cursor: pointer;
    background: #fff;
    transition: background 0.3s ease;
    margin-top: 20px;
    flex-direction: column;
    align-items: center;
  
}

.upload-box:hover, .upload-box-full:hover {
  background: #f9f9f9;
}

.upload-icon {
  font-size: 40px;
  color: #1976d2;
}

.upload-text {
  font-family: 'DM Sans', system-ui, -apple-system, sans-serif;
  font-weight: 600;
  font-size: 14px;
  line-height: 140%;
  letter-spacing: 0.4px;
  text-align: center;
  vertical-align: middle;
  color: #374151;
}

.upload-subtext {
  margin-top: 4px;
  font-size: 14px;
  color: #555;
   width: 392px;
  height: 21px;
  display: flex;             /* required for gap to take effect */
  gap: 8px;
  align-items: center;      /* optional, for vertical centering */
  opacity: 1;
  transform: rotate(0deg);
}
.doc-info{
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-left: 16px;

}
.choose-file, .capture-image {
  color: #1976d2;
  cursor: pointer;
  text-decoration: underline;
}

// new addd
.uploaded-docs {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.uploaded-item {
  display: flex;
  align-items: center;
  background: #f8f9fb;
  border-radius: 6px;
  padding: 10px 16px;
  gap: 12px;
}

.doc-preview {
  width: 40px;
  height: 40px;
  object-fit: cover;
  border-radius: 4px;
}

.doc-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.flex-info {
  flex-direction: row;
  gap: 24px;
  align-items: center;
}

.doc-name {
  font-weight: 600;
}

.doc-meta {
  font-size: 13px;
  color: #666;
}

.doc-type {
  padding: 6px;
  font-size: 14px;
  border-radius: 4px;
  border: 1px solid #ccc;
}

.icon-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
}

//new add 

/* ---------- Upload Documents Layout ---------- */
.upload-docs-wrapper {
  display: flex;
  gap: 24px;
  margin-top: 20px;
      /* width: 1225px; */
    height: 366px;
    background: transparent;

}

.uploaded-docs {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.uploaded-item {
  display: flex;
  align-items: center;
  gap: 12px;
  background: #f8f9fb;
  border-radius: 6px;
  padding: 8px 12px;
}

.doc-preview {
  width: 40px;
  height: 40px;
  border-radius: 4px;
  object-fit: cover;
}

.doc-type {
  flex: 1;
  padding: 6px;
  font-size: 14px;
  border: 1px solid #ccc;
  border-radius: 4px;
}

.doc-actions {
  display: flex;
  align-items: center;
     gap: 8px;
    position: relative;
    bottom: 34px;
}

.icon-btn {
  background: none;
  border: none;
  font-size: 16px;
  cursor: pointer;
}

/* Upload Box (Right Side) - Only used when documents are uploaded */
.upload-box {
  flex: 1;
}

/* Responsive */
@media (max-width: 768px) {
  .upload-docs-wrapper {
    flex-direction: column;
  }

  .upload-box {
    width: 100%;
  }
}

/* ---------- Uploaded Items List ---------- */
.uploaded-item {
  display: flex;
  align-items: center;
  gap: 16px;
  background: #F9FAFB;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  width: 545px;
  transition: all 0.3s ease;
  border: 1px solid #E5E7EB;
}

/* Edit Mode Styling */
.uploaded-item.editing-mode {
  height: 122px;
  background: #F9FAFB;
}

/* View Mode Styling */
.uploaded-item.view-mode {
  height: 64px;
  background: #F9FAFB;
  justify-content: space-between;
}

.doc-preview {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  object-fit: cover;
  flex-shrink: 0;
}

/* Edit Mode Info */
.doc-info-edit {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.doc-type-label {
  font-family: 'DM Sans', sans-serif;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin: 0;
}

.doc-type-select {
  width: 100%;
  height: 48px;
  padding: 12px 16px;
  border: 1px solid #D1D5DB;
  border-radius: 8px;
  font-family: 'DM Sans', sans-serif;
  font-size: 14px;
  background: white;
  color: #374151;
  outline: none;
  transition: border-color 0.2s ease;
}

.doc-type-select:focus {
  border-color: #3B82F6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* View Mode Info */
.doc-info-view {
  flex: 1;
  display: flex;
  align-items: center;
}

.doc-details-flex {
  display: flex;
  align-items: center;
  gap: 16px;
//  width: 229px;
}

.doc-name {
 font-family: 'DM Sans', sans-serif;
font-weight: 500;              /* Normal weight */
font-style: normal;            /* 'Regular' is usually 'normal' in CSS */
font-size: 16px;
line-height: 1.5;              /* 150% as a unitless multiplier */
letter-spacing: 0;             /* 0% translates to 0 (default) */
vertical-align: middle; 
color: #374151;  
  // min-width: 100px;
}

.doc-meta {
 font-family: 'DM Sans', sans-serif;
font-weight: 500;              /* Normal weight */
font-style: normal;            /* 'Regular' is usually 'normal' in CSS */
font-size: 16px;
line-height: 1.5;              /* 150% as a unitless multiplier */
letter-spacing: 0;             /* 0% translates to 0 (default) */
vertical-align: middle; 
color: #374151;  
  // min-width: 80px;
}

/* Actions */
.doc-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
  top: 1px;
}

.icon-btn {
  background: none;
  border: none;
  padding: 8px;
  cursor: pointer;
  border-radius: 6px;
  transition: background-color 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon-btn:hover {
  background-color: #F3F4F6;
}

.icon-btn img {
     width: 18.74px;
    height: 18.74px;
}

/* Specific button styles */
.save-btn:hover {
  background-color: #DCFCE7;
}

.cancel-btn:hover {
  background-color: #FEE2E2;
}

.edit-btn:hover {
  background-color: #DBEAFE;
}

.delete-btn:hover {
  background-color: #FEE2E2;
}

/* ---------- Camera Capture Box ---------- */
.camera-box {
  margin-top: 20px;
  text-align: center;
}

.preview-box {
  width: 300px;
  height: 200px;
  background: #d5d8de;
  margin: 0 auto 12px auto;
  border-radius: 6px;
}

.capture-actions {
  display: flex;
  justify-content: center;
  gap: 12px;
}

.btn-outline {
  background: none;
  border: 1px solid #ccc;
  border-radius: 4px;
  padding: 8px 16px;
  cursor: pointer;
}

/* ---------- Responsive ---------- */
@media (max-width: 1024px) {
  .form-row {
    flex-direction: column;
  }
}

@media (max-width: 768px) {
  .progress-steps {
    flex-direction: row;
    gap: 8px;
    padding: 12px 16px 24px 16px;
    justify-content: space-between;
  }

  .step {
    flex: 1;
    min-width: 0;
    max-width: 120px;
  }

  .step:not(:last-child)::after {
    width: calc(100% - 20px);
    left: 60%;
    right: -40%;
  }


  .label .step-title {
    font-size: 12px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .label .step-status {
    font-size: 10px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .form-actions {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    gap: 16px;
    padding: 16px 20px;
  }

  .btn-back,
  .btn-next,
  .btn-register {
    flex: 1;
    max-width: 150px;
    min-width: 100px;
  }
}

@media (max-width: 480px) {
  .container {
    width: 100%;
    height: 100vh;
    border-radius: 0;
  }

  .header-container {
    padding: 12px 16px;
  }

  .title {
    font-size: 18px;
  }

  .progress-steps {
    padding: 8px 12px 16px 12px;
    gap: 4px;
  }

  .step {
    flex: 1;
    min-width: 0;
  }

  .step .dot {
    width: 12px;
    height: 12px;
  }

  .step:not(:last-child)::after {
    height: 2px;
    top: 5px;
    width: calc(100% - 10px);
    left: 55%;
    right: -45%;
  }

  .label .step-title {
    font-size: 10px;
    line-height: 1.2;
  }

  .label .step-status {
    font-size: 8px;
    line-height: 1.2;
  }

  .form-section {
    padding: 0 12px 12px 12px;
  }

  .form-actions {
    padding: 12px 16px;
    gap: 12px;
  }

  .btn-back {
    min-width: 70px;
    padding: 10px 12px;
    font-size: 13px;
  }

  .btn-next,
  .btn-register {
    min-width: 90px;
    padding: 10px 16px;
    font-size: 13px;
  }

  .image-box {
    width: 90%;
    height: 200px;
  }

  .img-2 {
    width: 100%;
    height: auto;
  }

  .preview-box {
    width: 90%;
    height: 160px;
  }

  .actions {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .btn-retake,
  .btn-save,
  .btn-open-camera {
    width: 100%;
  }

  .capture-controls {
    width: 100%;
    justify-content: center;
  }

  .camera-dropdown {
    width: 100%;
    right: auto;
    left: 0;
  }
}

/* Android-specific styles */
@media (max-width: 768px) and (min-width: 481px) {
  .container {
    width: 100%;
    height: 100vh;
    border-radius: 0;
  }

  .header-container {
    padding: 16px 20px;
  }

  .progress-steps {
    padding: 12px 20px 20px 20px;
    gap: 12px;
  }

  .step:not(:last-child)::after {
    width: calc(100% - 30px);
    left: 65%;
    right: -35%;
  }

  .form-actions {
    padding: 16px 20px;
  }

  .image-container {
    margin-top: 10px;
  }

  .img-2 {
    width: 100%;
    max-width: 350px;
  }

  .image-box {
    width: 100%;
    height: 250px;
  }

  .btn-open-camera {
    width: 180px;
  }

  .actions {
    justify-content: center;
    gap: 16px;
  }

  .camera-dropdown {
    width: 280px;
  }
}

/* Extra small Android devices */
@media (max-width: 360px) {
  .progress-steps {
    padding: 6px 8px 12px 8px;
    gap: 2px;
  }

  .step .dot {
    width: 10px;
    height: 10px;
  }

  .step:not(:last-child)::after {
    height: 1px;
    top: 4px;
  }

  .label .step-title {
    font-size: 9px;
  }

  .label .step-status {
    font-size: 7px;
  }

  .form-actions {
    padding: 10px 12px;
  }

  .btn-back {
    min-width: 60px;
    font-size: 12px;
  }

  .btn-next,
  .btn-register {
    min-width: 80px;
    font-size: 12px;
  }
}
