import{A as Ke,B as Ue,C as Ze,D as Ge,E as Xe,F as Je,G as Qe,H as et,I as tt,J as it,K as at,L as nt,M as re,N as ot,O as rt,P as st,Q as lt,R as dt,a as q,b as R,c as N,d as Te,e as Se,f as Ve,g as ne,h as Ie,i as P,j as We,k as oe,l as ze,m as Fe,n as Le,o as Ee,p as O,q as Ye,r as Re,s as Be,t as He,u as $e,v as _e,w as K,x as U,y as qe,z as Ne}from"./chunk-GXFEW3CR.js";import{a as je}from"./chunk-E7ZOKENL.js";import{a as W}from"./chunk-SCKONRGC.js";import{d as pe,e as me,g as ue,h as ge,j as te}from"./chunk-DFAMUM5K.js";import{c as fe,d as be,e as ye}from"./chunk-HRORXLUI.js";import{h as ke,i as ve,k as we,l as Pe,m as ie,n as ae,o as Ce,p as De,s as Me,t as Ae}from"./chunk-PK2JKDOR.js";import{a as xe}from"./chunk-3OLZKZOW.js";import"./chunk-B7F22SOJ.js";import{b as Oe,c as _}from"./chunk-BMJVVMK5.js";import{a as j}from"./chunk-753GXAUT.js";import"./chunk-FPOZYJOD.js";import{h as ce,i as Y,l as he,m as ee}from"./chunk-XFXTD7QR.js";import"./chunk-WI5MSH4N.js";import"./chunk-JCOV4R6X.js";import"./chunk-CKP3SGE2.js";import{b as C,c as z,f as F,i as Z,k as B,m as o,n as G,o as X,p as x}from"./chunk-EHNA26RN.js";import{g as w}from"./chunk-2R6CW7ES.js";var wt=(t,e,i)=>!!(e&&e.year>t||i&&i.year<t),gt=(t,e,i,a)=>!!(t.day===null||a!==void 0&&!a.includes(t.day)||e&&R(t,e)||i&&N(t,i)),Pt=(t,e,i,a,n,r,s)=>{let d=(Array.isArray(i)?i:[i]).find(f=>q(e,f))!==void 0,c=q(e,a);return{disabled:gt(e,n,r,s),isActive:d,isToday:c,ariaSelected:d?"true":null,ariaLabel:Xe(t,c,e),text:e.day!=null?Qe(t,e):null}},J=(t,{minParts:e,maxParts:i})=>!!(wt(t.year,e,i)||e&&R(t,e)||i&&N(t,i)),Ct=(t,e,i)=>{let a=Object.assign(Object.assign({},K(t)),{day:null});return J(a,{minParts:e,maxParts:i})},Dt=(t,e)=>{let i=Object.assign(Object.assign({},U(t)),{day:null});return J(i,{maxParts:e})},Mt=(t,e,i)=>{if(Array.isArray(t)){let a=e.split("T")[0],n=t.find(r=>r.date===a);if(n)return{textColor:n.textColor,backgroundColor:n.backgroundColor}}else try{return t(e)}catch(a){z("[ion-datetime] - Exception thrown from provided `highlightedDates` callback. Please check your function and try again.",i,a)}},ct=(t,e)=>{var i,a,n,r;(!((i=e?.date)===null||i===void 0)&&i.timeZone||!((a=e?.date)===null||a===void 0)&&a.timeZoneName||!((n=e?.time)===null||n===void 0)&&n.timeZone||!((r=e?.time)===null||r===void 0)&&r.timeZoneName)&&C('[ion-datetime] - "timeZone" and "timeZoneName" are not supported in "formatOptions".',t)},se=(t,e,i)=>{if(i)switch(e){case"date":case"month-year":case"month":case"year":i.date===void 0&&C(`[ion-datetime] - The '${e}' presentation requires a date object in formatOptions.`,t);break;case"time":i.time===void 0&&C("[ion-datetime] - The 'time' presentation requires a time object in formatOptions.",t);break;case"date-time":case"time-date":i.date===void 0&&i.time===void 0&&C(`[ion-datetime] - The '${e}' presentation requires either a date or time object (or both) in formatOptions.`,t);break}},At=':host{display:-ms-flexbox;display:flex;-ms-flex-flow:column;flex-flow:column;background:var(--background);overflow:hidden}:host(.datetime-size-fixed){width:auto;height:auto}:host(.datetime-size-fixed:not(.datetime-prefer-wheel)){max-width:350px}:host(.datetime-size-fixed.datetime-prefer-wheel){min-width:350px;max-width:-webkit-max-content;max-width:-moz-max-content;max-width:max-content}:host(.datetime-size-cover){width:100%}:host .calendar-body,:host .datetime-year{opacity:0}:host(:not(.datetime-ready)) .datetime-year{position:absolute;pointer-events:none}:host(.datetime-ready) .calendar-body{opacity:1}:host(.datetime-ready) .datetime-year{display:none;opacity:1}:host .wheel-order-year-first .day-column{-ms-flex-order:3;order:3;text-align:end}:host .wheel-order-year-first .month-column{-ms-flex-order:2;order:2;text-align:end}:host .wheel-order-year-first .year-column{-ms-flex-order:1;order:1;text-align:start}:host .datetime-calendar,:host .datetime-year{display:-ms-flexbox;display:flex;-ms-flex:1 1 auto;flex:1 1 auto;-ms-flex-flow:column;flex-flow:column}:host(.show-month-and-year) .datetime-year{display:-ms-flexbox;display:flex}:host(.show-month-and-year) .calendar-next-prev,:host(.show-month-and-year) .calendar-days-of-week,:host(.show-month-and-year) .calendar-body,:host(.show-month-and-year) .datetime-time{display:none}:host(.month-year-picker-open) .datetime-footer{display:none}:host(.datetime-disabled){pointer-events:none}:host(.datetime-disabled) .calendar-days-of-week,:host(.datetime-disabled) .datetime-time{opacity:0.4}:host(.datetime-readonly){pointer-events:none;}:host(.datetime-readonly) .calendar-action-buttons,:host(.datetime-readonly) .calendar-body,:host(.datetime-readonly) .datetime-year{pointer-events:initial}:host(.datetime-readonly) .calendar-day[disabled]:not(.calendar-day-constrained),:host(.datetime-readonly) .datetime-action-buttons ion-button[disabled]{opacity:1}:host .datetime-header .datetime-title{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}:host .datetime-action-buttons.has-clear-button{width:100%}:host .datetime-action-buttons ion-buttons{display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between}.datetime-action-buttons .datetime-action-buttons-container{display:-ms-flexbox;display:flex}:host .calendar-action-buttons{display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between}:host .calendar-action-buttons ion-button{--background:transparent}:host .calendar-days-of-week{display:grid;grid-template-columns:repeat(7, 1fr);text-align:center}.calendar-days-of-week .day-of-week{-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:0;margin-bottom:0}:host .calendar-body{display:-ms-flexbox;display:flex;-ms-flex-positive:1;flex-grow:1;-webkit-scroll-snap-type:x mandatory;-ms-scroll-snap-type:x mandatory;scroll-snap-type:x mandatory;overflow-x:scroll;overflow-y:hidden;scrollbar-width:none;outline:none}:host .calendar-body .calendar-month{display:-ms-flexbox;display:flex;-ms-flex-flow:column;flex-flow:column;scroll-snap-align:start;scroll-snap-stop:always;-ms-flex-negative:0;flex-shrink:0;width:100%}:host .calendar-body .calendar-month-disabled{scroll-snap-align:none}:host .calendar-body::-webkit-scrollbar{display:none}:host .calendar-body .calendar-month-grid{display:grid;grid-template-columns:repeat(7, 1fr)}:host .calendar-day-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;min-width:0;min-height:0;overflow:visible}.calendar-day{border-radius:50%;-webkit-padding-start:0px;padding-inline-start:0px;-webkit-padding-end:0px;padding-inline-end:0px;padding-top:0px;padding-bottom:0px;-webkit-margin-start:0px;margin-inline-start:0px;-webkit-margin-end:0px;margin-inline-end:0px;margin-top:0px;margin-bottom:0px;display:-ms-flexbox;display:flex;position:relative;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;border:none;outline:none;background:none;color:currentColor;font-family:var(--ion-font-family, inherit);cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none;z-index:0}:host .calendar-day[disabled]{pointer-events:none;opacity:0.4}.calendar-day:not(.calendar-day-adjacent-day):focus{background:rgba(var(--ion-color-base-rgb), 0.2);-webkit-box-shadow:0px 0px 0px 4px rgba(var(--ion-color-base-rgb), 0.2);box-shadow:0px 0px 0px 4px rgba(var(--ion-color-base-rgb), 0.2)}:host .datetime-time{display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between}:host(.datetime-presentation-time) .datetime-time{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0}:host ion-popover{--height:200px}:host .time-header{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center}:host .time-body{border-radius:8px;-webkit-padding-start:12px;padding-inline-start:12px;-webkit-padding-end:12px;padding-inline-end:12px;padding-top:6px;padding-bottom:6px;display:-ms-flexbox;display:flex;border:none;background:var(--ion-color-step-300, var(--ion-background-color-step-300, #edeef0));color:var(--ion-text-color, #000);font-family:inherit;font-size:inherit;cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none}:host .time-body-active{color:var(--ion-color-base)}:host(.in-item){position:static}:host(.show-month-and-year) .calendar-action-buttons .calendar-month-year-toggle{color:var(--ion-color-base)}.calendar-month-year{min-width:0}.calendar-month-year-toggle{font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;position:relative;border:0;outline:none;background:transparent;cursor:pointer;z-index:1}.calendar-month-year-toggle::after{left:0;right:0;top:0;bottom:0;position:absolute;content:"";opacity:0;-webkit-transition:opacity 15ms linear, background-color 15ms linear;transition:opacity 15ms linear, background-color 15ms linear;z-index:-1}.calendar-month-year-toggle.ion-focused::after{background:currentColor}.calendar-month-year-toggle:disabled{opacity:0.3;pointer-events:none}.calendar-month-year-toggle ion-icon{-webkit-padding-start:4px;padding-inline-start:4px;-webkit-padding-end:0;padding-inline-end:0;padding-top:0;padding-bottom:0;-ms-flex-negative:0;flex-shrink:0}.calendar-month-year-toggle #toggle-wrapper{display:-ms-inline-flexbox;display:inline-flex;-ms-flex-align:center;align-items:center}ion-picker{--highlight-background:var(--wheel-highlight-background);--highlight-border-radius:var(--wheel-highlight-border-radius);--fade-background-rgb:var(--wheel-fade-background-rgb)}:host{--background:var(--ion-color-light, #f4f5f8);--background-rgb:var(--ion-color-light-rgb, 244, 245, 248);--title-color:var(--ion-color-step-600, var(--ion-text-color-step-400, #666666))}:host(.datetime-presentation-date-time:not(.datetime-prefer-wheel)),:host(.datetime-presentation-time-date:not(.datetime-prefer-wheel)),:host(.datetime-presentation-date:not(.datetime-prefer-wheel)){min-height:350px}:host .datetime-header{-webkit-padding-start:16px;padding-inline-start:16px;-webkit-padding-end:16px;padding-inline-end:16px;padding-top:16px;padding-bottom:16px;border-bottom:0.55px solid var(--ion-color-step-200, var(--ion-background-color-step-200, #cccccc));font-size:min(0.875rem, 22.4px)}:host .datetime-header .datetime-title{color:var(--title-color)}:host .datetime-header .datetime-selected-date{margin-top:10px}.calendar-month-year-toggle{-webkit-padding-start:16px;padding-inline-start:16px;-webkit-padding-end:16px;padding-inline-end:16px;padding-top:0px;padding-bottom:0px;min-height:44px;font-size:min(1rem, 25.6px);font-weight:600}.calendar-month-year-toggle.ion-focused::after{opacity:0.15}.calendar-month-year-toggle #toggle-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:8px;margin-inline-end:8px;margin-top:10px;margin-bottom:10px}:host .calendar-action-buttons .calendar-month-year-toggle ion-icon,:host .calendar-action-buttons ion-buttons ion-button{color:var(--ion-color-base)}:host .calendar-action-buttons ion-buttons{padding-left:0;padding-right:0;padding-top:8px;padding-bottom:0}:host .calendar-action-buttons ion-buttons ion-button{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}:host .calendar-days-of-week{-webkit-padding-start:8px;padding-inline-start:8px;-webkit-padding-end:8px;padding-inline-end:8px;padding-top:0;padding-bottom:0;color:var(--ion-color-step-300, var(--ion-text-color-step-700, #b3b3b3));font-size:min(0.75rem, 19.2px);font-weight:600;line-height:24px;text-transform:uppercase}@supports (border-radius: mod(1px, 1px)){.calendar-days-of-week .day-of-week{width:clamp(20px, calc(mod(min(1rem, 24px), 24px) * 10), 100%);height:24px;overflow:hidden}.calendar-day{border-radius:max(8px, mod(min(1rem, 24px), 24px) * 10)}}@supports ((border-radius: mod(1px, 1px)) and (background: -webkit-named-image(apple-pay-logo-black)) and (not (contain-intrinsic-size: none))) or (not (border-radius: mod(1px, 1px))){.calendar-days-of-week .day-of-week{width:auto;height:auto;overflow:initial}.calendar-day{border-radius:32px}}:host .calendar-body .calendar-month .calendar-month-grid{-webkit-padding-start:8px;padding-inline-start:8px;-webkit-padding-end:8px;padding-inline-end:8px;padding-top:8px;padding-bottom:8px;-ms-flex-align:center;align-items:center;height:calc(100% - 16px)}:host .calendar-day-wrapper{-webkit-padding-start:4px;padding-inline-start:4px;-webkit-padding-end:4px;padding-inline-end:4px;padding-top:4px;padding-bottom:4px;height:0;min-height:1rem}:host .calendar-day{width:40px;min-width:40px;height:40px;font-size:min(1.25rem, 32px)}.calendar-day.calendar-day-active{background:rgba(var(--ion-color-base-rgb), 0.2);font-size:min(1.375rem, 35.2px)}:host .calendar-day.calendar-day-today{color:var(--ion-color-base)}:host .calendar-day.calendar-day-active,:host .calendar-day.calendar-day-adjacent-day.calendar-day-active{color:var(--ion-color-base);font-weight:600}:host .calendar-day.calendar-day-today.calendar-day-active{background:var(--ion-color-base);color:var(--ion-color-contrast)}:host .calendar-day.calendar-day-adjacent-day{color:var(--ion-color-step-300, var(--ion-text-color-step-700, #b3b3b3))}:host .datetime-time{-webkit-padding-start:16px;padding-inline-start:16px;-webkit-padding-end:16px;padding-inline-end:16px;padding-top:8px;padding-bottom:16px;font-size:min(1rem, 25.6px)}:host .datetime-time .time-header{font-weight:600}:host .datetime-buttons{-webkit-padding-start:8px;padding-inline-start:8px;-webkit-padding-end:8px;padding-inline-end:8px;padding-top:8px;padding-bottom:8px;border-top:0.55px solid var(--ion-color-step-200, var(--ion-background-color-step-200, #cccccc))}:host .datetime-buttons ::slotted(ion-buttons),:host .datetime-buttons ion-buttons{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between}:host .datetime-action-buttons{width:100%}',Ot=':host{display:-ms-flexbox;display:flex;-ms-flex-flow:column;flex-flow:column;background:var(--background);overflow:hidden}:host(.datetime-size-fixed){width:auto;height:auto}:host(.datetime-size-fixed:not(.datetime-prefer-wheel)){max-width:350px}:host(.datetime-size-fixed.datetime-prefer-wheel){min-width:350px;max-width:-webkit-max-content;max-width:-moz-max-content;max-width:max-content}:host(.datetime-size-cover){width:100%}:host .calendar-body,:host .datetime-year{opacity:0}:host(:not(.datetime-ready)) .datetime-year{position:absolute;pointer-events:none}:host(.datetime-ready) .calendar-body{opacity:1}:host(.datetime-ready) .datetime-year{display:none;opacity:1}:host .wheel-order-year-first .day-column{-ms-flex-order:3;order:3;text-align:end}:host .wheel-order-year-first .month-column{-ms-flex-order:2;order:2;text-align:end}:host .wheel-order-year-first .year-column{-ms-flex-order:1;order:1;text-align:start}:host .datetime-calendar,:host .datetime-year{display:-ms-flexbox;display:flex;-ms-flex:1 1 auto;flex:1 1 auto;-ms-flex-flow:column;flex-flow:column}:host(.show-month-and-year) .datetime-year{display:-ms-flexbox;display:flex}:host(.show-month-and-year) .calendar-next-prev,:host(.show-month-and-year) .calendar-days-of-week,:host(.show-month-and-year) .calendar-body,:host(.show-month-and-year) .datetime-time{display:none}:host(.month-year-picker-open) .datetime-footer{display:none}:host(.datetime-disabled){pointer-events:none}:host(.datetime-disabled) .calendar-days-of-week,:host(.datetime-disabled) .datetime-time{opacity:0.4}:host(.datetime-readonly){pointer-events:none;}:host(.datetime-readonly) .calendar-action-buttons,:host(.datetime-readonly) .calendar-body,:host(.datetime-readonly) .datetime-year{pointer-events:initial}:host(.datetime-readonly) .calendar-day[disabled]:not(.calendar-day-constrained),:host(.datetime-readonly) .datetime-action-buttons ion-button[disabled]{opacity:1}:host .datetime-header .datetime-title{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}:host .datetime-action-buttons.has-clear-button{width:100%}:host .datetime-action-buttons ion-buttons{display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between}.datetime-action-buttons .datetime-action-buttons-container{display:-ms-flexbox;display:flex}:host .calendar-action-buttons{display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between}:host .calendar-action-buttons ion-button{--background:transparent}:host .calendar-days-of-week{display:grid;grid-template-columns:repeat(7, 1fr);text-align:center}.calendar-days-of-week .day-of-week{-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:0;margin-bottom:0}:host .calendar-body{display:-ms-flexbox;display:flex;-ms-flex-positive:1;flex-grow:1;-webkit-scroll-snap-type:x mandatory;-ms-scroll-snap-type:x mandatory;scroll-snap-type:x mandatory;overflow-x:scroll;overflow-y:hidden;scrollbar-width:none;outline:none}:host .calendar-body .calendar-month{display:-ms-flexbox;display:flex;-ms-flex-flow:column;flex-flow:column;scroll-snap-align:start;scroll-snap-stop:always;-ms-flex-negative:0;flex-shrink:0;width:100%}:host .calendar-body .calendar-month-disabled{scroll-snap-align:none}:host .calendar-body::-webkit-scrollbar{display:none}:host .calendar-body .calendar-month-grid{display:grid;grid-template-columns:repeat(7, 1fr)}:host .calendar-day-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;min-width:0;min-height:0;overflow:visible}.calendar-day{border-radius:50%;-webkit-padding-start:0px;padding-inline-start:0px;-webkit-padding-end:0px;padding-inline-end:0px;padding-top:0px;padding-bottom:0px;-webkit-margin-start:0px;margin-inline-start:0px;-webkit-margin-end:0px;margin-inline-end:0px;margin-top:0px;margin-bottom:0px;display:-ms-flexbox;display:flex;position:relative;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;border:none;outline:none;background:none;color:currentColor;font-family:var(--ion-font-family, inherit);cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none;z-index:0}:host .calendar-day[disabled]{pointer-events:none;opacity:0.4}.calendar-day:not(.calendar-day-adjacent-day):focus{background:rgba(var(--ion-color-base-rgb), 0.2);-webkit-box-shadow:0px 0px 0px 4px rgba(var(--ion-color-base-rgb), 0.2);box-shadow:0px 0px 0px 4px rgba(var(--ion-color-base-rgb), 0.2)}:host .datetime-time{display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between}:host(.datetime-presentation-time) .datetime-time{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0}:host ion-popover{--height:200px}:host .time-header{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center}:host .time-body{border-radius:8px;-webkit-padding-start:12px;padding-inline-start:12px;-webkit-padding-end:12px;padding-inline-end:12px;padding-top:6px;padding-bottom:6px;display:-ms-flexbox;display:flex;border:none;background:var(--ion-color-step-300, var(--ion-background-color-step-300, #edeef0));color:var(--ion-text-color, #000);font-family:inherit;font-size:inherit;cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none}:host .time-body-active{color:var(--ion-color-base)}:host(.in-item){position:static}:host(.show-month-and-year) .calendar-action-buttons .calendar-month-year-toggle{color:var(--ion-color-base)}.calendar-month-year{min-width:0}.calendar-month-year-toggle{font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;position:relative;border:0;outline:none;background:transparent;cursor:pointer;z-index:1}.calendar-month-year-toggle::after{left:0;right:0;top:0;bottom:0;position:absolute;content:"";opacity:0;-webkit-transition:opacity 15ms linear, background-color 15ms linear;transition:opacity 15ms linear, background-color 15ms linear;z-index:-1}.calendar-month-year-toggle.ion-focused::after{background:currentColor}.calendar-month-year-toggle:disabled{opacity:0.3;pointer-events:none}.calendar-month-year-toggle ion-icon{-webkit-padding-start:4px;padding-inline-start:4px;-webkit-padding-end:0;padding-inline-end:0;padding-top:0;padding-bottom:0;-ms-flex-negative:0;flex-shrink:0}.calendar-month-year-toggle #toggle-wrapper{display:-ms-inline-flexbox;display:inline-flex;-ms-flex-align:center;align-items:center}ion-picker{--highlight-background:var(--wheel-highlight-background);--highlight-border-radius:var(--wheel-highlight-border-radius);--fade-background-rgb:var(--wheel-fade-background-rgb)}:host{--background:var(--ion-color-step-100, var(--ion-background-color-step-100, #ffffff));--title-color:var(--ion-color-contrast)}:host .datetime-header{-webkit-padding-start:20px;padding-inline-start:20px;-webkit-padding-end:20px;padding-inline-end:20px;padding-top:20px;padding-bottom:20px;background:var(--ion-color-base);color:var(--title-color)}:host .datetime-header .datetime-title{font-size:0.75rem;text-transform:uppercase}:host .datetime-header .datetime-selected-date{margin-top:30px;font-size:2.125rem}:host .calendar-action-buttons ion-button{--color:var(--ion-color-step-650, var(--ion-text-color-step-350, #595959))}.calendar-month-year-toggle{-webkit-padding-start:20px;padding-inline-start:20px;-webkit-padding-end:16px;padding-inline-end:16px;padding-top:12px;padding-bottom:12px;min-height:48px;background:transparent;color:var(--ion-color-step-650, var(--ion-text-color-step-350, #595959));z-index:1}.calendar-month-year-toggle.ion-focused::after{opacity:0.04}.calendar-month-year-toggle ion-ripple-effect{color:currentColor}@media (any-hover: hover){.calendar-month-year-toggle.ion-activatable:not(.ion-focused):hover::after{background:currentColor;opacity:0.04}}:host .calendar-days-of-week{-webkit-padding-start:10px;padding-inline-start:10px;-webkit-padding-end:10px;padding-inline-end:10px;padding-top:0px;padding-bottom:0px;color:var(--ion-color-step-500, var(--ion-text-color-step-500, gray));font-size:0.875rem;line-height:36px}:host .calendar-body .calendar-month .calendar-month-grid{-webkit-padding-start:10px;padding-inline-start:10px;-webkit-padding-end:10px;padding-inline-end:10px;padding-top:4px;padding-bottom:4px;grid-template-rows:repeat(6, 1fr)}:host .calendar-day{width:42px;min-width:42px;height:42px;font-size:0.875rem}:host .calendar-day.calendar-day-today{border:1px solid var(--ion-color-base);color:var(--ion-color-base)}:host .calendar-day.calendar-day-active,:host .calendar-day.calendar-day-adjacent-day.calendar-day-active{color:var(--ion-color-contrast)}.calendar-day.calendar-day-active,.calendar-day.calendar-day-active:focus{border:1px solid var(--ion-color-base);background:var(--ion-color-base)}:host .calendar-day.calendar-day-adjacent-day{color:var(--ion-color-step-500, var(--ion-text-color-step-500, gray))}:host .datetime-time{-webkit-padding-start:16px;padding-inline-start:16px;-webkit-padding-end:16px;padding-inline-end:16px;padding-top:8px;padding-bottom:8px}:host .time-header{color:var(--ion-color-step-650, var(--ion-text-color-step-350, #595959))}:host(.datetime-presentation-month) .datetime-year,:host(.datetime-presentation-year) .datetime-year,:host(.datetime-presentation-month-year) .datetime-year{margin-top:20px;margin-bottom:20px}:host .datetime-buttons{-webkit-padding-start:10px;padding-inline-start:10px;-webkit-padding-end:10px;padding-inline-end:10px;padding-top:10px;padding-bottom:10px;display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:end;justify-content:flex-end}',jt=class{constructor(t){Z(this,t),this.ionCancel=x(this,"ionCancel",7),this.ionChange=x(this,"ionChange",7),this.ionValueChange=x(this,"ionValueChange",7),this.ionFocus=x(this,"ionFocus",7),this.ionBlur=x(this,"ionBlur",7),this.ionStyle=x(this,"ionStyle",7),this.ionRender=x(this,"ionRender",7),this.inputId=`ion-dt-${Tt++}`,this.prevPresentation=null,this.showMonthAndYear=!1,this.activeParts=[],this.workingParts={month:5,day:28,year:2021,hour:13,minute:52,ampm:"pm",isAdjacentDay:!1},this.isTimePopoverOpen=!1,this.color="primary",this.name=this.inputId,this.disabled=!1,this.readonly=!1,this.showAdjacentDays=!1,this.presentation="date-time",this.cancelText="Cancel",this.doneText="Done",this.clearText="Clear",this.locale="default",this.firstDayOfWeek=0,this.multiple=!1,this.showDefaultTitle=!1,this.showDefaultButtons=!1,this.showClearButton=!1,this.showDefaultTimeLabel=!0,this.size="fixed",this.preferWheel=!1,this.warnIfIncorrectValueUsage=()=>{let{multiple:e,value:i}=this;!e&&Array.isArray(i)&&C(`[ion-datetime] - An array of values was passed, but multiple is "false". This is incorrect usage and may result in unexpected behaviors. To dismiss this warning, pass a string to the "value" property when multiple="false".

  Value Passed: [${i.map(a=>`'${a}'`).join(", ")}]
`,this.el)},this.setValue=e=>{this.value=e,this.ionChange.emit({value:e})},this.getActivePartsWithFallback=()=>{var e;let{defaultParts:i}=this;return(e=this.getActivePart())!==null&&e!==void 0?e:i},this.getActivePart=()=>{let{activeParts:e}=this;return Array.isArray(e)?e[0]:e},this.closeParentOverlay=e=>{let i=this.el.closest("ion-modal, ion-popover");i&&i.dismiss(void 0,e)},this.setWorkingParts=e=>{this.workingParts=Object.assign({},e)},this.setActiveParts=(e,i=!1)=>{if(this.readonly)return;let{multiple:a,minParts:n,maxParts:r,activeParts:s}=this,l=Ue(e,n,r);if(this.setWorkingParts(l),a){let c=Array.isArray(s)?s:[s];i?this.activeParts=c.filter(h=>!q(h,l)):this.activeParts=[...c,l]}else this.activeParts=Object.assign({},l);this.el.querySelector('[slot="buttons"]')!==null||this.showDefaultButtons||this.confirm()},this.initializeKeyboardListeners=()=>{let e=this.calendarBodyRef;if(!e)return;let i=this.el.shadowRoot,a=e.querySelector(".calendar-month:nth-of-type(2)"),n=s=>{var l;!((l=s[0].oldValue)===null||l===void 0)&&l.includes("ion-focused")||!e.classList.contains("ion-focused")||this.focusWorkingDay(a)},r=new MutationObserver(n);r.observe(e,{attributeFilter:["class"],attributeOldValue:!0}),this.destroyKeyboardMO=()=>{r?.disconnect()},e.addEventListener("keydown",s=>{let l=i.activeElement;if(!l||!l.classList.contains("calendar-day"))return;let d=We(l),c;switch(s.key){case"ArrowDown":s.preventDefault(),c=_e(d);break;case"ArrowUp":s.preventDefault(),c=$e(d);break;case"ArrowRight":s.preventDefault(),c=Be(d);break;case"ArrowLeft":s.preventDefault(),c=He(d);break;case"Home":s.preventDefault(),c=Ye(d);break;case"End":s.preventDefault(),c=Re(d);break;case"PageUp":s.preventDefault(),c=s.shiftKey?qe(d):K(d);break;case"PageDown":s.preventDefault(),c=s.shiftKey?Ne(d):U(d);break;default:return}gt(c,this.minParts,this.maxParts)||(this.setWorkingParts(Object.assign(Object.assign({},this.workingParts),c)),requestAnimationFrame(()=>this.focusWorkingDay(a)))})},this.focusWorkingDay=e=>{let{day:i,month:a,year:n}=this.workingParts,r=new Date(`${a}/1/${n}`).getDay(),s=r>=this.firstDayOfWeek?r-this.firstDayOfWeek:7-(this.firstDayOfWeek-r);if(i===null)return;let l=e.querySelector(`.calendar-day-wrapper:nth-of-type(${s+i}) .calendar-day`);l&&l.focus()},this.processMinParts=()=>{let{min:e,defaultParts:i}=this;if(e===void 0){this.minParts=void 0;return}this.minParts=Ee(e,i)},this.processMaxParts=()=>{let{max:e,defaultParts:i}=this;if(e===void 0){this.maxParts=void 0;return}this.maxParts=Le(e,i)},this.initializeCalendarListener=()=>{let e=this.calendarBodyRef;if(!e)return;let i=e.querySelectorAll(".calendar-month"),a=i[0],n=i[1],r=i[2],l=F(this)==="ios"&&typeof navigator<"u"&&navigator.maxTouchPoints>1;B(()=>{e.scrollLeft=a.clientWidth*(W(this.el)?-1:1);let d=p=>{let g=e.getBoundingClientRect(),u=(W(this.el)?e.scrollLeft>=-2:e.scrollLeft<=2)?a:r,y=u.getBoundingClientRect();if(Math.abs(y.x-g.x)>2)return;let{forceRenderDate:k}=this;return k!==void 0?{month:k.month,year:k.year,day:k.day}:u===a?K(p):u===r?U(p):void 0},c=()=>{l&&(e.style.removeProperty("pointer-events"),f=!1);let p=d(this.workingParts);if(!p)return;let{month:g,day:b,year:u}=p;J({month:g,year:u,day:null},{minParts:Object.assign(Object.assign({},this.minParts),{day:null}),maxParts:Object.assign(Object.assign({},this.maxParts),{day:null})})||(e.style.setProperty("overflow","hidden"),B(()=>{this.setWorkingParts(Object.assign(Object.assign({},this.workingParts),{month:g,day:b,year:u})),e.scrollLeft=n.clientWidth*(W(this.el)?-1:1),e.style.removeProperty("overflow"),this.resolveForceDateScrolling&&this.resolveForceDateScrolling()}))},h,f=!1,m=()=>{h&&clearTimeout(h),!f&&l&&(e.style.setProperty("pointer-events","none"),f=!0),h=setTimeout(c,50)};e.addEventListener("scroll",m),this.destroyCalendarListener=()=>{e.removeEventListener("scroll",m)}})},this.destroyInteractionListeners=()=>{let{destroyCalendarListener:e,destroyKeyboardMO:i}=this;e!==void 0&&e(),i!==void 0&&i()},this.processValue=e=>{let i=e!=null&&e!==""&&(!Array.isArray(e)||e.length>0),a=i?oe(e):this.defaultParts,{minParts:n,maxParts:r,workingParts:s,el:l}=this;if(this.warnIfIncorrectValueUsage(),!a)return;i&&Te(a,n,r);let d=Array.isArray(a)?a[a.length-1]:a,c=ze(d,n,r),{month:h,day:f,year:m,hour:p,minute:g}=c,b=Fe(p);i?Array.isArray(a)?this.activeParts=[...a]:this.activeParts={month:h,day:f,year:m,hour:p,minute:g,ampm:b}:this.activeParts=[];let u=h!==void 0&&h!==s.month||m!==void 0&&m!==s.year,y=l.classList.contains("datetime-ready"),{isGridStyle:k,showMonthAndYear:M}=this;k&&u&&y&&!M?this.animateToDate(c):this.setWorkingParts({month:h,day:f,year:m,hour:p,minute:g,ampm:b})},this.animateToDate=e=>w(this,null,function*(){let{workingParts:i}=this;this.forceRenderDate=e;let a=new Promise(r=>{this.resolveForceDateScrolling=r});R(e,i)?this.prevMonth():this.nextMonth(),yield a,this.resolveForceDateScrolling=void 0,this.forceRenderDate=void 0}),this.onFocus=()=>{this.ionFocus.emit()},this.onBlur=()=>{this.ionBlur.emit()},this.hasValue=()=>this.value!=null,this.nextMonth=()=>{let e=this.calendarBodyRef;if(!e)return;let i=e.querySelector(".calendar-month:last-of-type");if(!i)return;let a=i.offsetWidth*2;e.scrollTo({top:0,left:a*(W(this.el)?-1:1),behavior:"smooth"})},this.prevMonth=()=>{let e=this.calendarBodyRef;!e||!e.querySelector(".calendar-month:first-of-type")||e.scrollTo({top:0,left:0,behavior:"smooth"})},this.toggleMonthAndYearView=()=>{this.showMonthAndYear=!this.showMonthAndYear}}formatOptionsChanged(){let{el:t,formatOptions:e,presentation:i}=this;se(t,i,e),ct(t,e)}disabledChanged(){this.emitStyle()}minChanged(){this.processMinParts()}maxChanged(){this.processMaxParts()}presentationChanged(){let{el:t,formatOptions:e,presentation:i}=this;se(t,i,e)}get isGridStyle(){let{presentation:t,preferWheel:e}=this;return(t==="date"||t==="date-time"||t==="time-date")&&!e}yearValuesChanged(){this.parsedYearValues=P(this.yearValues)}monthValuesChanged(){this.parsedMonthValues=P(this.monthValues)}dayValuesChanged(){this.parsedDayValues=P(this.dayValues)}hourValuesChanged(){this.parsedHourValues=P(this.hourValues)}minuteValuesChanged(){this.parsedMinuteValues=P(this.minuteValues)}valueChanged(){return w(this,null,function*(){let{value:t}=this;this.hasValue()&&this.processValue(t),this.emitStyle(),this.ionValueChange.emit({value:t})})}confirm(t=!1){return w(this,null,function*(){let{isCalendarPicker:e,activeParts:i,preferWheel:a,workingParts:n}=this;(i!==void 0||!e)&&(Array.isArray(i)&&i.length===0?a?this.setValue(O(n)):this.setValue(void 0):this.setValue(O(i))),t&&this.closeParentOverlay(Vt)})}reset(t){return w(this,null,function*(){this.processValue(t)})}cancel(t=!1){return w(this,null,function*(){this.ionCancel.emit(),t&&this.closeParentOverlay(St)})}get isCalendarPicker(){let{presentation:t}=this;return t==="date"||t==="date-time"||t==="time-date"}connectedCallback(){this.clearFocusVisible=je(this.el).destroy}disconnectedCallback(){this.clearFocusVisible&&(this.clearFocusVisible(),this.clearFocusVisible=void 0)}initializeListeners(){this.initializeCalendarListener(),this.initializeKeyboardListeners()}componentDidLoad(){let{el:t,intersectionTrackerRef:e}=this,i=l=>{l[0].isIntersecting&&(this.initializeListeners(),B(()=>{this.el.classList.add("datetime-ready")}))},a=new IntersectionObserver(i,{threshold:.01,root:t});Y(()=>a?.observe(e));let n=l=>{l[0].isIntersecting||(this.destroyInteractionListeners(),this.showMonthAndYear=!1,B(()=>{this.el.classList.remove("datetime-ready")}))},r=new IntersectionObserver(n,{threshold:0,root:t});Y(()=>r?.observe(e));let s=ce(this.el);s.addEventListener("ionFocus",l=>l.stopPropagation()),s.addEventListener("ionBlur",l=>l.stopPropagation())}componentDidRender(){let{presentation:t,prevPresentation:e,calendarBodyRef:i,minParts:a,preferWheel:n,forceRenderDate:r}=this,s=!n&&["date-time","time-date","date"].includes(t);if(a!==void 0&&s&&i){let l=i.querySelector(".calendar-month:nth-of-type(1)");l&&r===void 0&&(i.scrollLeft=l.clientWidth*(W(this.el)?-1:1))}if(e===null){this.prevPresentation=t;return}t!==e&&(this.prevPresentation=t,this.destroyInteractionListeners(),this.initializeListeners(),this.showMonthAndYear=!1,Y(()=>{this.ionRender.emit()}))}componentWillLoad(){let{el:t,formatOptions:e,highlightedDates:i,multiple:a,presentation:n,preferWheel:r}=this;a&&(n!=="date"&&C('[ion-datetime] - Multiple date selection is only supported for presentation="date".',t),r&&C('[ion-datetime] - Multiple date selection is not supported with preferWheel="true".',t)),i!==void 0&&(n!=="date"&&n!=="date-time"&&n!=="time-date"&&C("[ion-datetime] - The highlightedDates property is only supported with the date, date-time, and time-date presentations.",t),r&&C('[ion-datetime] - The highlightedDates property is not supported with preferWheel="true".',t)),e&&(se(t,n,e),ct(t,e));let s=this.parsedHourValues=P(this.hourValues),l=this.parsedMinuteValues=P(this.minuteValues),d=this.parsedMonthValues=P(this.monthValues),c=this.parsedYearValues=P(this.yearValues),h=this.parsedDayValues=P(this.dayValues),f=this.todayParts=oe(it());this.processMinParts(),this.processMaxParts(),this.defaultParts=Ze({refParts:f,monthValues:d,dayValues:h,yearValues:c,hourValues:s,minuteValues:l,minParts:this.minParts,maxParts:this.maxParts}),this.processValue(this.value),this.emitStyle()}emitStyle(){this.ionStyle.emit({interactive:!0,datetime:!0,"interactive-disabled":this.disabled})}renderFooter(){let{disabled:t,readonly:e,showDefaultButtons:i,showClearButton:a}=this,n=t||e;if(!(this.el.querySelector('[slot="buttons"]')!==null)&&!i&&!a)return;let s=()=>{this.reset(),this.setValue(void 0)};return o("div",{class:"datetime-footer"},o("div",{class:"datetime-buttons"},o("div",{class:{"datetime-action-buttons":!0,"has-clear-button":this.showClearButton}},o("slot",{name:"buttons"},o("ion-buttons",null,i&&o("ion-button",{id:"cancel-button",color:this.color,onClick:()=>this.cancel(!0),disabled:n},this.cancelText),o("div",{class:"datetime-action-buttons-container"},a&&o("ion-button",{id:"clear-button",color:this.color,onClick:()=>s(),disabled:n},this.clearText),i&&o("ion-button",{id:"confirm-button",color:this.color,onClick:()=>this.confirm(!0),disabled:n},this.doneText)))))))}renderWheelPicker(t=this.presentation){let e=t==="time-date"?[this.renderTimePickerColumns(t),this.renderDatePickerColumns(t)]:[this.renderDatePickerColumns(t),this.renderTimePickerColumns(t)];return o("ion-picker",null,e)}renderDatePickerColumns(t){return t==="date-time"||t==="time-date"?this.renderCombinedDatePickerColumn():this.renderIndividualDatePickerColumns(t)}renderCombinedDatePickerColumn(){let{defaultParts:t,disabled:e,workingParts:i,locale:a,minParts:n,maxParts:r,todayParts:s,isDateEnabled:l}=this,d=this.getActivePartsWithFallback(),c=re(i),h=c[c.length-1];c[0].day=1,h.day=Ve(h.month,h.year);let f=n!==void 0&&N(n,c[0])?n:c[0],m=r!==void 0&&R(r,h)?r:h,p=lt(a,s,f,m,this.parsedDayValues,this.parsedMonthValues),g=p.items,b=p.parts;l&&(g=g.map((y,k)=>{let M=b[k],S;try{S=!l(O(M))}catch(D){z("[ion-datetime] - Exception thrown from provided `isDateEnabled` function. Please check your function and try again.",D)}return Object.assign(Object.assign({},y),{disabled:S})}));let u=i.day!==null?`${i.year}-${i.month}-${i.day}`:`${t.year}-${t.month}-${t.day}`;return o("ion-picker-column",{"aria-label":"Select a date",class:"date-column",color:this.color,disabled:e,value:u,onIonChange:y=>{let{value:k}=y.detail,M=b.find(({month:S,day:D,year:V})=>k===`${V}-${S}-${D}`);this.setWorkingParts(Object.assign(Object.assign({},i),M)),this.setActiveParts(Object.assign(Object.assign({},d),M)),y.stopPropagation()}},g.map(y=>o("ion-picker-column-option",{part:y.value===u?`${v} ${T}`:v,key:y.value,disabled:y.disabled,value:y.value},y.text)))}renderIndividualDatePickerColumns(t){let{workingParts:e,isDateEnabled:i}=this,n=t!=="year"&&t!=="time"?ot(this.locale,e,this.minParts,this.maxParts,this.parsedMonthValues):[],s=t==="date"?rt(this.locale,e,this.minParts,this.maxParts,this.parsedDayValues):[];i&&(s=s.map(f=>{let{value:m}=f,p=typeof m=="string"?parseInt(m):m,g={month:e.month,day:p,year:e.year},b;try{b=!i(O(g))}catch(u){z("[ion-datetime] - Exception thrown from provided `isDateEnabled` function. Please check your function and try again.",u)}return Object.assign(Object.assign({},f),{disabled:b})}));let d=t!=="month"&&t!=="time"?st(this.locale,this.defaultParts,this.minParts,this.maxParts,this.parsedYearValues):[],c=ne(this.locale,{month:"numeric",day:"numeric"}),h=[];return c?h=[this.renderMonthPickerColumn(n),this.renderDayPickerColumn(s),this.renderYearPickerColumn(d)]:h=[this.renderDayPickerColumn(s),this.renderMonthPickerColumn(n),this.renderYearPickerColumn(d)],h}renderDayPickerColumn(t){var e;if(t.length===0)return[];let{disabled:i,workingParts:a}=this,n=this.getActivePartsWithFallback(),r=(e=a.day!==null?a.day:this.defaultParts.day)!==null&&e!==void 0?e:void 0;return o("ion-picker-column",{"aria-label":"Select a day",class:"day-column",color:this.color,disabled:i,value:r,onIonChange:s=>{this.setWorkingParts(Object.assign(Object.assign({},a),{day:s.detail.value})),this.setActiveParts(Object.assign(Object.assign({},n),{day:s.detail.value})),s.stopPropagation()}},t.map(s=>o("ion-picker-column-option",{part:s.value===r?`${v} ${T}`:v,key:s.value,disabled:s.disabled,value:s.value},s.text)))}renderMonthPickerColumn(t){if(t.length===0)return[];let{disabled:e,workingParts:i}=this,a=this.getActivePartsWithFallback();return o("ion-picker-column",{"aria-label":"Select a month",class:"month-column",color:this.color,disabled:e,value:i.month,onIonChange:n=>{this.setWorkingParts(Object.assign(Object.assign({},i),{month:n.detail.value})),this.setActiveParts(Object.assign(Object.assign({},a),{month:n.detail.value})),n.stopPropagation()}},t.map(n=>o("ion-picker-column-option",{part:n.value===i.month?`${v} ${T}`:v,key:n.value,disabled:n.disabled,value:n.value},n.text)))}renderYearPickerColumn(t){if(t.length===0)return[];let{disabled:e,workingParts:i}=this,a=this.getActivePartsWithFallback();return o("ion-picker-column",{"aria-label":"Select a year",class:"year-column",color:this.color,disabled:e,value:i.year,onIonChange:n=>{this.setWorkingParts(Object.assign(Object.assign({},i),{year:n.detail.value})),this.setActiveParts(Object.assign(Object.assign({},a),{year:n.detail.value})),n.stopPropagation()}},t.map(n=>o("ion-picker-column-option",{part:n.value===i.year?`${v} ${T}`:v,key:n.value,disabled:n.disabled,value:n.value},n.text)))}renderTimePickerColumns(t){if(["date","month","month-year","year"].includes(t))return[];let i=this.getActivePart()!==void 0,{hoursData:a,minutesData:n,dayPeriodData:r}=dt(this.locale,this.workingParts,this.hourCycle,i?this.minParts:void 0,i?this.maxParts:void 0,this.parsedHourValues,this.parsedMinuteValues);return[this.renderHourPickerColumn(a),this.renderMinutePickerColumn(n),this.renderDayPeriodPickerColumn(r)]}renderHourPickerColumn(t){let{disabled:e,workingParts:i}=this;if(t.length===0)return[];let a=this.getActivePartsWithFallback();return o("ion-picker-column",{"aria-label":"Select an hour",color:this.color,disabled:e,value:a.hour,numericInput:!0,onIonChange:n=>{this.setWorkingParts(Object.assign(Object.assign({},i),{hour:n.detail.value})),this.setActiveParts(Object.assign(Object.assign({},this.getActivePartsWithFallback()),{hour:n.detail.value})),n.stopPropagation()}},t.map(n=>o("ion-picker-column-option",{part:n.value===a.hour?`${v} ${T}`:v,key:n.value,disabled:n.disabled,value:n.value},n.text)))}renderMinutePickerColumn(t){let{disabled:e,workingParts:i}=this;if(t.length===0)return[];let a=this.getActivePartsWithFallback();return o("ion-picker-column",{"aria-label":"Select a minute",color:this.color,disabled:e,value:a.minute,numericInput:!0,onIonChange:n=>{this.setWorkingParts(Object.assign(Object.assign({},i),{minute:n.detail.value})),this.setActiveParts(Object.assign(Object.assign({},this.getActivePartsWithFallback()),{minute:n.detail.value})),n.stopPropagation()}},t.map(n=>o("ion-picker-column-option",{part:n.value===a.minute?`${v} ${T}`:v,key:n.value,disabled:n.disabled,value:n.value},n.text)))}renderDayPeriodPickerColumn(t){let{disabled:e,workingParts:i}=this;if(t.length===0)return[];let a=this.getActivePartsWithFallback(),n=Ie(this.locale);return o("ion-picker-column",{"aria-label":"Select a day period",style:n?{order:"-1"}:{},color:this.color,disabled:e,value:a.ampm,onIonChange:r=>{let s=Ke(i,r.detail.value);this.setWorkingParts(Object.assign(Object.assign({},i),{ampm:r.detail.value,hour:s})),this.setActiveParts(Object.assign(Object.assign({},this.getActivePartsWithFallback()),{ampm:r.detail.value,hour:s})),r.stopPropagation()}},t.map(r=>o("ion-picker-column-option",{part:r.value===a.ampm?`${v} ${T}`:v,key:r.value,disabled:r.disabled,value:r.value},r.text)))}renderWheelView(t){let{locale:e}=this,a=ne(e)?"month-first":"year-first";return o("div",{class:{[`wheel-order-${a}`]:!0}},this.renderWheelPicker(t))}renderCalendarHeader(t){let{disabled:e}=this,i=t==="ios"?ge:me,a=t==="ios"?te:pe,n=e||Ct(this.workingParts,this.minParts,this.maxParts),r=e||Dt(this.workingParts,this.maxParts),s=this.el.getAttribute("dir")||void 0;return o("div",{class:"calendar-header"},o("div",{class:"calendar-action-buttons"},o("div",{class:"calendar-month-year"},o("button",{class:{"calendar-month-year-toggle":!0,"ion-activatable":!0,"ion-focusable":!0},part:"month-year-button",disabled:e,"aria-label":this.showMonthAndYear?"Hide year picker":"Show year picker",onClick:()=>this.toggleMonthAndYearView()},o("span",{id:"toggle-wrapper"},Je(this.locale,this.workingParts),o("ion-icon",{"aria-hidden":"true",icon:this.showMonthAndYear?i:a,lazy:!1,flipRtl:!0})),t==="md"&&o("ion-ripple-effect",null))),o("div",{class:"calendar-next-prev"},o("ion-buttons",null,o("ion-button",{"aria-label":"Previous month",disabled:n,onClick:()=>this.prevMonth()},o("ion-icon",{dir:s,"aria-hidden":"true",slot:"icon-only",icon:ue,lazy:!1,flipRtl:!0})),o("ion-button",{"aria-label":"Next month",disabled:r,onClick:()=>this.nextMonth()},o("ion-icon",{dir:s,"aria-hidden":"true",slot:"icon-only",icon:te,lazy:!1,flipRtl:!0}))))),o("div",{class:"calendar-days-of-week","aria-hidden":"true"},at(this.locale,t,this.firstDayOfWeek%7).map(l=>o("div",{class:"day-of-week"},l))))}renderMonth(t,e){let{disabled:i,readonly:a}=this,n=this.parsedYearValues===void 0||this.parsedYearValues.includes(e),r=this.parsedMonthValues===void 0||this.parsedMonthValues.includes(t),s=!n||!r,l=i||a,d=i||J({month:t,year:e,day:null},{minParts:Object.assign(Object.assign({},this.minParts),{day:null}),maxParts:Object.assign(Object.assign({},this.maxParts),{day:null})}),c=this.workingParts.month===t&&this.workingParts.year===e,h=this.getActivePartsWithFallback();return o("div",{"aria-hidden":c?null:"true",class:{"calendar-month":!0,"calendar-month-disabled":!c&&d}},o("div",{class:"calendar-month-grid"},nt(t,e,this.firstDayOfWeek%7,this.showAdjacentDays).map((f,m)=>{let{day:p,dayOfWeek:g,isAdjacentDay:b}=f,{el:u,highlightedDates:y,isDateEnabled:k,multiple:M,showAdjacentDays:S}=this,D=t,V=e;S&&b&&p!==null&&(p>20?t===1?(V=e-1,D=12):D=t-1:p<15&&(t===12?(V=e+1,D=1):D=t+1));let A={month:D,day:p,year:V,isAdjacentDay:b},H=p===null,{isActive:$,isToday:le,ariaLabel:ft,ariaSelected:bt,disabled:yt,text:xt}=Pt(this.locale,A,this.activeParts,this.todayParts,this.minParts,this.maxParts,this.parsedDayValues),de=O(A),I=s||yt;if(!I&&k!==void 0)try{I=!k(de)}catch(E){z("[ion-datetime] - Exception thrown from provided `isDateEnabled` function. Please check your function and try again.",u,E)}let kt=I&&l,vt=I||l,L;y!==void 0&&!$&&p!==null&&!b&&(L=Mt(y,de,u));let Q;return!H&&!b?Q=`calendar-day${$?" active":""}${le?" today":""}${I?" disabled":""}`:b&&(Q=`calendar-day${I?" disabled":""}`),o("div",{class:"calendar-day-wrapper"},o("button",{ref:E=>{E&&(E.style.setProperty("color",`${L?L.textColor:""}`,"important"),E.style.setProperty("background-color",`${L?L.backgroundColor:""}`,"important"))},tabindex:"-1","data-day":p,"data-month":D,"data-year":V,"data-index":m,"data-day-of-week":g,disabled:vt,class:{"calendar-day-padding":H,"calendar-day":!0,"calendar-day-active":$,"calendar-day-constrained":kt,"calendar-day-today":le,"calendar-day-adjacent-day":b},part:Q,"aria-hidden":H?"true":null,"aria-selected":bt,"aria-label":ft,onClick:()=>{H||(b?(this.el.blur(),this.activeParts=Object.assign(Object.assign({},h),A),this.animateToDate(A),this.confirm()):(this.setWorkingParts(Object.assign(Object.assign({},this.workingParts),A)),M?this.setActiveParts(A,$):this.setActiveParts(Object.assign(Object.assign({},h),A))))}},xt))})))}renderCalendarBody(){return o("div",{class:"calendar-body ion-focusable",ref:t=>this.calendarBodyRef=t,tabindex:"0"},re(this.workingParts,this.forceRenderDate).map(({month:t,year:e})=>this.renderMonth(t,e)))}renderCalendar(t){return o("div",{class:"datetime-calendar",key:"datetime-calendar"},this.renderCalendarHeader(t),this.renderCalendarBody())}renderTimeLabel(){if(!(!(this.el.querySelector('[slot="time-label"]')!==null)&&!this.showDefaultTimeLabel))return o("slot",{name:"time-label"},"Time")}renderTimeOverlay(){let{disabled:t,hourCycle:e,isTimePopoverOpen:i,locale:a,formatOptions:n}=this,r=Se(a,e),s=this.getActivePartsWithFallback();return[o("div",{class:"time-header"},this.renderTimeLabel()),o("button",{class:{"time-body":!0,"time-body-active":i},part:`time-button${i?" active":""}`,"aria-expanded":"false","aria-haspopup":"true",disabled:t,onClick:l=>w(this,null,function*(){let{popoverRef:d}=this;d&&(this.isTimePopoverOpen=!0,d.present(new CustomEvent("ionShadowTarget",{detail:{ionShadowTarget:l.target}})),yield d.onWillDismiss(),this.isTimePopoverOpen=!1)})},Ge(a,s,r,n?.time)),o("ion-popover",{alignment:"center",translucent:!0,overlayIndex:1,arrow:!1,onWillPresent:l=>{l.target.querySelectorAll("ion-picker-column").forEach(c=>c.scrollActiveItemIntoView())},style:{"--offset-y":"-10px","--min-width":"fit-content"},keyboardEvents:!0,ref:l=>this.popoverRef=l},this.renderWheelPicker("time"))]}getHeaderSelectedDateText(){var t;let{activeParts:e,formatOptions:i,multiple:a,titleSelectedDatesFormatter:n}=this,r=Array.isArray(e),s;if(a&&r&&e.length!==1){if(s=`${e.length} days`,n!==void 0)try{s=n(O(e))}catch(l){z("[ion-datetime] - Exception in provided `titleSelectedDatesFormatter`:",l)}}else s=et(this.locale,this.getActivePartsWithFallback(),(t=i?.date)!==null&&t!==void 0?t:{weekday:"short",month:"short",day:"numeric"});return s}renderHeader(t=!0){if(!(!(this.el.querySelector('[slot="title"]')!==null)&&!this.showDefaultTitle))return o("div",{class:"datetime-header"},o("div",{class:"datetime-title"},o("slot",{name:"title"},"Select Date")),t&&o("div",{class:"datetime-selected-date"},this.getHeaderSelectedDateText()))}renderTime(){let{presentation:t}=this;return o("div",{class:"datetime-time"},t==="time"?this.renderWheelPicker():this.renderTimeOverlay())}renderCalendarViewMonthYearPicker(){return o("div",{class:"datetime-year"},this.renderWheelView("month-year"))}renderDatetime(t){let{presentation:e,preferWheel:i}=this;if(i&&(e==="date"||e==="date-time"||e==="time-date"))return[this.renderHeader(!1),this.renderWheelView(),this.renderFooter()];switch(e){case"date-time":return[this.renderHeader(),this.renderCalendar(t),this.renderCalendarViewMonthYearPicker(),this.renderTime(),this.renderFooter()];case"time-date":return[this.renderHeader(),this.renderTime(),this.renderCalendar(t),this.renderCalendarViewMonthYearPicker(),this.renderFooter()];case"time":return[this.renderHeader(!1),this.renderTime(),this.renderFooter()];case"month":case"month-year":case"year":return[this.renderHeader(!1),this.renderWheelView(),this.renderFooter()];default:return[this.renderHeader(),this.renderCalendar(t),this.renderCalendarViewMonthYearPicker(),this.renderFooter()]}}render(){let{name:t,value:e,disabled:i,el:a,color:n,readonly:r,showMonthAndYear:s,preferWheel:l,presentation:d,size:c,isGridStyle:h}=this,f=F(this),m=d==="year"||d==="month"||d==="month-year",p=s||m,g=s&&!m,u=(d==="date"||d==="date-time"||d==="time-date")&&l;return he(!0,a,t,tt(e),i),o(G,{key:"79677f5bc0fb32fb68569636bd76e68238e62eb8","aria-disabled":i?"true":null,onFocus:this.onFocus,onBlur:this.onBlur,class:Object.assign({},Oe(n,{[f]:!0,"datetime-readonly":r,"datetime-disabled":i,"show-month-and-year":p,"month-year-picker-open":g,[`datetime-presentation-${d}`]:!0,[`datetime-size-${c}`]:!0,"datetime-prefer-wheel":u,"datetime-grid":h}))},o("div",{key:"bf07b1e3c64af6e837663ff470bea93787a6e86f",class:"intersection-tracker",ref:y=>this.intersectionTrackerRef=y}),this.renderDatetime(f))}get el(){return X(this)}static get watchers(){return{formatOptions:["formatOptionsChanged"],disabled:["disabledChanged"],min:["minChanged"],max:["maxChanged"],presentation:["presentationChanged"],yearValues:["yearValuesChanged"],monthValues:["monthValuesChanged"],dayValues:["dayValuesChanged"],hourValues:["hourValuesChanged"],minuteValues:["minuteValuesChanged"],value:["valueChanged"]}}},Tt=0,St="datetime-cancel",Vt="datetime-confirm",v="wheel-item",T="active";jt.style={ios:At,md:Ot};var ht=t=>{let e=j(),i=j(),a=j();return i.addElement(t.querySelector("ion-backdrop")).fromTo("opacity",.01,"var(--backdrop-opacity)").beforeStyles({"pointer-events":"none"}).afterClearStyles(["pointer-events"]),a.addElement(t.querySelector(".picker-wrapper")).fromTo("transform","translateY(100%)","translateY(0%)"),e.addElement(t).easing("cubic-bezier(.36,.66,.04,1)").duration(400).addAnimation([i,a])},pt=t=>{let e=j(),i=j(),a=j();return i.addElement(t.querySelector("ion-backdrop")).fromTo("opacity","var(--backdrop-opacity)",.01),a.addElement(t.querySelector(".picker-wrapper")).fromTo("transform","translateY(0%)","translateY(100%)"),e.addElement(t).easing("cubic-bezier(.36,.66,.04,1)").duration(400).addAnimation([i,a])},It=".sc-ion-picker-legacy-ios-h{--border-radius:0;--border-style:solid;--min-width:auto;--width:100%;--max-width:500px;--min-height:auto;--max-height:auto;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;top:0;display:block;position:absolute;width:100%;height:100%;outline:none;font-family:var(--ion-font-family, inherit);contain:strict;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:1001}.sc-ion-picker-legacy-ios-h{inset-inline-start:0}.overlay-hidden.sc-ion-picker-legacy-ios-h{display:none}.picker-wrapper.sc-ion-picker-legacy-ios{border-radius:var(--border-radius);left:0;right:0;bottom:0;-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:auto;margin-bottom:auto;-webkit-transform:translate3d(0,  100%,  0);transform:translate3d(0,  100%,  0);display:-ms-flexbox;display:flex;position:absolute;-ms-flex-direction:column;flex-direction:column;width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--background);contain:strict;overflow:hidden;z-index:10}.picker-toolbar.sc-ion-picker-legacy-ios{width:100%;background:transparent;contain:strict;z-index:1}.picker-button.sc-ion-picker-legacy-ios{border:0;font-family:inherit}.picker-button.sc-ion-picker-legacy-ios:active,.picker-button.sc-ion-picker-legacy-ios:focus{outline:none}.picker-columns.sc-ion-picker-legacy-ios{display:-ms-flexbox;display:flex;position:relative;-ms-flex-pack:center;justify-content:center;margin-bottom:var(--ion-safe-area-bottom, 0);contain:strict;overflow:hidden}.picker-above-highlight.sc-ion-picker-legacy-ios,.picker-below-highlight.sc-ion-picker-legacy-ios{display:none;pointer-events:none}.sc-ion-picker-legacy-ios-h{--background:var(--ion-background-color, #fff);--border-width:1px 0 0;--border-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, var(--ion-background-color-step-250, #c8c7cc))));--height:260px;--backdrop-opacity:var(--ion-backdrop-opacity, 0.26);color:var(--ion-item-color, var(--ion-text-color, #000))}.picker-toolbar.sc-ion-picker-legacy-ios{display:-ms-flexbox;display:flex;height:44px;border-bottom:0.55px solid var(--border-color)}.picker-toolbar-button.sc-ion-picker-legacy-ios{-ms-flex:1;flex:1;text-align:end}.picker-toolbar-button.sc-ion-picker-legacy-ios:last-child .picker-button.sc-ion-picker-legacy-ios{font-weight:600}.picker-toolbar-button.sc-ion-picker-legacy-ios:first-child{font-weight:normal;text-align:start}.picker-button.sc-ion-picker-legacy-ios,.picker-button.ion-activated.sc-ion-picker-legacy-ios{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-padding-start:1em;padding-inline-start:1em;-webkit-padding-end:1em;padding-inline-end:1em;padding-top:0;padding-bottom:0;height:44px;background:transparent;color:var(--ion-color-primary, #0054e9);font-size:16px}.picker-columns.sc-ion-picker-legacy-ios{height:215px;-webkit-perspective:1000px;perspective:1000px}.picker-above-highlight.sc-ion-picker-legacy-ios{top:0;-webkit-transform:translate3d(0,  0,  90px);transform:translate3d(0,  0,  90px);display:block;position:absolute;width:100%;height:81px;border-bottom:1px solid var(--border-color);background:-webkit-gradient(linear, left top, left bottom, color-stop(20%, var(--background, var(--ion-background-color, #fff))), to(rgba(var(--background-rgb, var(--ion-background-color-rgb, 255, 255, 255)), 0.8)));background:linear-gradient(to bottom, var(--background, var(--ion-background-color, #fff)) 20%, rgba(var(--background-rgb, var(--ion-background-color-rgb, 255, 255, 255)), 0.8) 100%);z-index:10}.picker-above-highlight.sc-ion-picker-legacy-ios{inset-inline-start:0}.picker-below-highlight.sc-ion-picker-legacy-ios{top:115px;-webkit-transform:translate3d(0,  0,  90px);transform:translate3d(0,  0,  90px);display:block;position:absolute;width:100%;height:119px;border-top:1px solid var(--border-color);background:-webkit-gradient(linear, left bottom, left top, color-stop(30%, var(--background, var(--ion-background-color, #fff))), to(rgba(var(--background-rgb, var(--ion-background-color-rgb, 255, 255, 255)), 0.8)));background:linear-gradient(to top, var(--background, var(--ion-background-color, #fff)) 30%, rgba(var(--background-rgb, var(--ion-background-color-rgb, 255, 255, 255)), 0.8) 100%);z-index:11}.picker-below-highlight.sc-ion-picker-legacy-ios{inset-inline-start:0}",Wt=".sc-ion-picker-legacy-md-h{--border-radius:0;--border-style:solid;--min-width:auto;--width:100%;--max-width:500px;--min-height:auto;--max-height:auto;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;top:0;display:block;position:absolute;width:100%;height:100%;outline:none;font-family:var(--ion-font-family, inherit);contain:strict;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:1001}.sc-ion-picker-legacy-md-h{inset-inline-start:0}.overlay-hidden.sc-ion-picker-legacy-md-h{display:none}.picker-wrapper.sc-ion-picker-legacy-md{border-radius:var(--border-radius);left:0;right:0;bottom:0;-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:auto;margin-bottom:auto;-webkit-transform:translate3d(0,  100%,  0);transform:translate3d(0,  100%,  0);display:-ms-flexbox;display:flex;position:absolute;-ms-flex-direction:column;flex-direction:column;width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--background);contain:strict;overflow:hidden;z-index:10}.picker-toolbar.sc-ion-picker-legacy-md{width:100%;background:transparent;contain:strict;z-index:1}.picker-button.sc-ion-picker-legacy-md{border:0;font-family:inherit}.picker-button.sc-ion-picker-legacy-md:active,.picker-button.sc-ion-picker-legacy-md:focus{outline:none}.picker-columns.sc-ion-picker-legacy-md{display:-ms-flexbox;display:flex;position:relative;-ms-flex-pack:center;justify-content:center;margin-bottom:var(--ion-safe-area-bottom, 0);contain:strict;overflow:hidden}.picker-above-highlight.sc-ion-picker-legacy-md,.picker-below-highlight.sc-ion-picker-legacy-md{display:none;pointer-events:none}.sc-ion-picker-legacy-md-h{--background:var(--ion-background-color, #fff);--border-width:0.55px 0 0;--border-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-150, var(--ion-background-color-step-150, rgba(0, 0, 0, 0.13)))));--height:260px;--backdrop-opacity:var(--ion-backdrop-opacity, 0.26);color:var(--ion-item-color, var(--ion-text-color, #000))}.picker-toolbar.sc-ion-picker-legacy-md{display:-ms-flexbox;display:flex;-ms-flex-pack:end;justify-content:flex-end;height:44px}.picker-button.sc-ion-picker-legacy-md,.picker-button.ion-activated.sc-ion-picker-legacy-md{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-padding-start:1.1em;padding-inline-start:1.1em;-webkit-padding-end:1.1em;padding-inline-end:1.1em;padding-top:0;padding-bottom:0;height:44px;background:transparent;color:var(--ion-color-primary, #0054e9);font-size:14px;font-weight:500;text-transform:uppercase;-webkit-box-shadow:none;box-shadow:none}.picker-columns.sc-ion-picker-legacy-md{height:216px;-webkit-perspective:1800px;perspective:1800px}.picker-above-highlight.sc-ion-picker-legacy-md{top:0;-webkit-transform:translate3d(0,  0,  90px);transform:translate3d(0,  0,  90px);position:absolute;width:100%;height:81px;border-bottom:1px solid var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-150, var(--ion-background-color-step-150, rgba(0, 0, 0, 0.13)))));background:-webkit-gradient(linear, left top, left bottom, color-stop(20%, var(--ion-background-color, #fff)), to(rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.8)));background:linear-gradient(to bottom, var(--ion-background-color, #fff) 20%, rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.8) 100%);z-index:10}.picker-above-highlight.sc-ion-picker-legacy-md{inset-inline-start:0}.picker-below-highlight.sc-ion-picker-legacy-md{top:115px;-webkit-transform:translate3d(0,  0,  90px);transform:translate3d(0,  0,  90px);position:absolute;width:100%;height:119px;border-top:1px solid var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-150, var(--ion-background-color-step-150, rgba(0, 0, 0, 0.13)))));background:-webkit-gradient(linear, left bottom, left top, color-stop(30%, var(--ion-background-color, #fff)), to(rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.8)));background:linear-gradient(to top, var(--ion-background-color, #fff) 30%, rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.8) 100%);z-index:11}.picker-below-highlight.sc-ion-picker-legacy-md{inset-inline-start:0}",zt=class{constructor(t){Z(this,t),this.didPresent=x(this,"ionPickerDidPresent",7),this.willPresent=x(this,"ionPickerWillPresent",7),this.willDismiss=x(this,"ionPickerWillDismiss",7),this.didDismiss=x(this,"ionPickerDidDismiss",7),this.didPresentShorthand=x(this,"didPresent",7),this.willPresentShorthand=x(this,"willPresent",7),this.willDismissShorthand=x(this,"willDismiss",7),this.didDismissShorthand=x(this,"didDismiss",7),this.delegateController=Me(this),this.lockController=xe(),this.triggerController=Ae(),this.presented=!1,this.hasController=!1,this.keyboardClose=!0,this.buttons=[],this.columns=[],this.duration=0,this.showBackdrop=!0,this.backdropDismiss=!0,this.animated=!0,this.isOpen=!1,this.onBackdropTap=()=>{this.dismiss(void 0,De)},this.dispatchCancelHandler=e=>{let i=e.detail.role;if(ae(i)){let a=this.buttons.find(n=>n.role==="cancel");this.callButtonHandler(a)}}}onIsOpenChange(t,e){t===!0&&e===!1?this.present():t===!1&&e===!0&&this.dismiss()}triggerChanged(){let{trigger:t,el:e,triggerController:i}=this;t&&i.addClickListener(e,t)}connectedCallback(){ke(this.el),this.triggerChanged()}disconnectedCallback(){this.triggerController.removeClickListener()}componentWillLoad(){var t;!((t=this.htmlAttributes)===null||t===void 0)&&t.id||ve(this.el)}componentDidLoad(){C("[ion-picker-legacy] - ion-picker-legacy and ion-picker-legacy-column have been deprecated in favor of new versions of the ion-picker and ion-picker-column components. These new components display inline with your page content allowing for more presentation flexibility than before.",this.el),this.isOpen===!0&&Y(()=>this.present()),this.triggerChanged()}present(){return w(this,null,function*(){let t=yield this.lockController.lock();yield this.delegateController.attachViewToDom(),yield we(this,"pickerEnter",ht,ht,void 0),this.duration>0&&(this.durationTimeout=setTimeout(()=>this.dismiss(),this.duration)),t()})}dismiss(t,e){return w(this,null,function*(){let i=yield this.lockController.lock();this.durationTimeout&&clearTimeout(this.durationTimeout);let a=yield Pe(this,t,e,"pickerLeave",pt,pt);return a&&this.delegateController.removeViewFromDom(),i(),a})}onDidDismiss(){return ie(this.el,"ionPickerDidDismiss")}onWillDismiss(){return ie(this.el,"ionPickerWillDismiss")}getColumn(t){return Promise.resolve(this.columns.find(e=>e.name===t))}buttonClick(t){return w(this,null,function*(){let e=t.role;return ae(e)?this.dismiss(void 0,e):(yield this.callButtonHandler(t))?this.dismiss(this.getSelected(),t.role):Promise.resolve()})}callButtonHandler(t){return w(this,null,function*(){return!(t&&(yield Ce(t.handler,this.getSelected()))===!1)})}getSelected(){let t={};return this.columns.forEach((e,i)=>{let a=e.selectedIndex!==void 0?e.options[e.selectedIndex]:void 0;t[e.name]={text:a?a.text:void 0,value:a?a.value:void 0,columnIndex:i}}),t}render(){let{htmlAttributes:t}=this,e=F(this);return o(G,Object.assign({key:"b95440747eb80cba23ae676d399d5e5816722c58","aria-modal":"true",tabindex:"-1"},t,{style:{zIndex:`${2e4+this.overlayIndex}`},class:Object.assign({[e]:!0,[`picker-${e}`]:!0,"overlay-hidden":!0},_(this.cssClass)),onIonBackdropTap:this.onBackdropTap,onIonPickerWillDismiss:this.dispatchCancelHandler}),o("ion-backdrop",{key:"169d1c83ef40e7fcb134219a585298b403a70b0f",visible:this.showBackdrop,tappable:this.backdropDismiss}),o("div",{key:"98518e5f5cea2dfb8dfa63d9545e9ae3a5765023",tabindex:"0","aria-hidden":"true"}),o("div",{key:"151755ab8eb23f9adafbfe201349398f5a69dee7",class:"picker-wrapper ion-overlay-wrapper",role:"dialog"},o("div",{key:"5dcf93b2f4fe8f4fce7c7aec8f85ef45a03ef470",class:"picker-toolbar"},this.buttons.map(i=>o("div",{class:Ft(i)},o("button",{type:"button",onClick:()=>this.buttonClick(i),class:Lt(i)},i.text)))),o("div",{key:"fd5d66708edd38adc5a4d2fad7298969398a05e3",class:"picker-columns"},o("div",{key:"1b5830fd6cef1016af7736792c514965d6cb38a8",class:"picker-above-highlight"}),this.presented&&this.columns.map(i=>o("ion-picker-legacy-column",{col:i})),o("div",{key:"c6edeca7afd69e13c9c66ba36f261974fd0f8f78",class:"picker-below-highlight"}))),o("div",{key:"e2a4b24710e30579b14b82dbfd3761b2187797b5",tabindex:"0","aria-hidden":"true"}))}get el(){return X(this)}static get watchers(){return{isOpen:["onIsOpenChange"],trigger:["triggerChanged"]}}},Ft=t=>({[`picker-toolbar-${t.role}`]:t.role!==void 0,"picker-toolbar-button":!0}),Lt=t=>Object.assign({"picker-button":!0,"ion-activatable":!0},_(t.cssClass));zt.style={ios:It,md:Wt};var Et=".picker-col{display:-ms-flexbox;display:flex;position:relative;-ms-flex:1;flex:1;-ms-flex-pack:center;justify-content:center;height:100%;-webkit-box-sizing:content-box;box-sizing:content-box;contain:content}.picker-opts{position:relative;-ms-flex:1;flex:1;max-width:100%}.picker-opt{top:0;display:block;position:absolute;width:100%;border:0;text-align:center;text-overflow:ellipsis;white-space:nowrap;contain:strict;overflow:hidden;will-change:transform}.picker-opt{inset-inline-start:0}.picker-opt.picker-opt-disabled{pointer-events:none}.picker-opt-disabled{opacity:0}.picker-opts-left{-ms-flex-pack:start;justify-content:flex-start}.picker-opts-right{-ms-flex-pack:end;justify-content:flex-end}.picker-opt:active,.picker-opt:focus{outline:none}.picker-prefix{position:relative;-ms-flex:1;flex:1;text-align:end;white-space:nowrap}.picker-suffix{position:relative;-ms-flex:1;flex:1;text-align:start;white-space:nowrap}.picker-col{-webkit-padding-start:4px;padding-inline-start:4px;-webkit-padding-end:4px;padding-inline-end:4px;padding-top:0;padding-bottom:0;-webkit-transform-style:preserve-3d;transform-style:preserve-3d}.picker-prefix,.picker-suffix,.picker-opts{top:77px;-webkit-transform-style:preserve-3d;transform-style:preserve-3d;color:inherit;font-size:20px;line-height:42px;pointer-events:none}.picker-opt{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-transform-origin:center center;transform-origin:center center;height:46px;-webkit-transform-style:preserve-3d;transform-style:preserve-3d;-webkit-transition-timing-function:ease-out;transition-timing-function:ease-out;background:transparent;color:inherit;font-size:20px;line-height:42px;-webkit-backface-visibility:hidden;backface-visibility:hidden;pointer-events:auto}:host-context([dir=rtl]) .picker-opt{-webkit-transform-origin:calc(100% - center) center;transform-origin:calc(100% - center) center}[dir=rtl] .picker-opt{-webkit-transform-origin:calc(100% - center) center;transform-origin:calc(100% - center) center}@supports selector(:dir(rtl)){.picker-opt:dir(rtl){-webkit-transform-origin:calc(100% - center) center;transform-origin:calc(100% - center) center}}",Yt=".picker-col{display:-ms-flexbox;display:flex;position:relative;-ms-flex:1;flex:1;-ms-flex-pack:center;justify-content:center;height:100%;-webkit-box-sizing:content-box;box-sizing:content-box;contain:content}.picker-opts{position:relative;-ms-flex:1;flex:1;max-width:100%}.picker-opt{top:0;display:block;position:absolute;width:100%;border:0;text-align:center;text-overflow:ellipsis;white-space:nowrap;contain:strict;overflow:hidden;will-change:transform}.picker-opt{inset-inline-start:0}.picker-opt.picker-opt-disabled{pointer-events:none}.picker-opt-disabled{opacity:0}.picker-opts-left{-ms-flex-pack:start;justify-content:flex-start}.picker-opts-right{-ms-flex-pack:end;justify-content:flex-end}.picker-opt:active,.picker-opt:focus{outline:none}.picker-prefix{position:relative;-ms-flex:1;flex:1;text-align:end;white-space:nowrap}.picker-suffix{position:relative;-ms-flex:1;flex:1;text-align:start;white-space:nowrap}.picker-col{-webkit-padding-start:8px;padding-inline-start:8px;-webkit-padding-end:8px;padding-inline-end:8px;padding-top:0;padding-bottom:0;-webkit-transform-style:preserve-3d;transform-style:preserve-3d}.picker-prefix,.picker-suffix,.picker-opts{top:77px;-webkit-transform-style:preserve-3d;transform-style:preserve-3d;color:inherit;font-size:22px;line-height:42px;pointer-events:none}.picker-opt{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;height:43px;-webkit-transition-timing-function:ease-out;transition-timing-function:ease-out;background:transparent;color:inherit;font-size:22px;line-height:42px;-webkit-backface-visibility:hidden;backface-visibility:hidden;pointer-events:auto}.picker-prefix,.picker-suffix,.picker-opt.picker-opt-selected{color:var(--ion-color-primary, #0054e9)}",Rt=class{constructor(t){Z(this,t),this.ionPickerColChange=x(this,"ionPickerColChange",7),this.optHeight=0,this.rotateFactor=0,this.scaleFactor=1,this.velocity=0,this.y=0,this.noAnimate=!0,this.colDidChange=!1}colChanged(){this.colDidChange=!0}connectedCallback(){return w(this,null,function*(){let t=0,e=.81;F(this)==="ios"&&(t=-.46,e=1),this.rotateFactor=t,this.scaleFactor=e,this.gesture=(yield import("./chunk-Z4ZXIL76.js")).createGesture({el:this.el,gestureName:"picker-swipe",gesturePriority:100,threshold:0,passive:!1,onStart:a=>this.onStart(a),onMove:a=>this.onMove(a),onEnd:a=>this.onEnd(a)}),this.gesture.enable(),this.tmrId=setTimeout(()=>{this.noAnimate=!1,this.refresh(!0)},250)})}componentDidLoad(){this.onDomChange()}componentDidUpdate(){this.colDidChange&&(this.onDomChange(!0,!1),this.colDidChange=!1)}disconnectedCallback(){this.rafId!==void 0&&cancelAnimationFrame(this.rafId),this.tmrId&&clearTimeout(this.tmrId),this.gesture&&(this.gesture.destroy(),this.gesture=void 0)}emitColChange(){this.ionPickerColChange.emit(this.col)}setSelected(t,e){let i=t>-1?-(t*this.optHeight):0;this.velocity=0,this.rafId!==void 0&&cancelAnimationFrame(this.rafId),this.update(i,e,!0),this.emitColChange()}update(t,e,i){if(!this.optsEl)return;let a=0,n=0,{col:r,rotateFactor:s}=this,l=r.selectedIndex,d=r.selectedIndex=this.indexForY(-t),c=e===0?"":e+"ms",h=`scale(${this.scaleFactor})`,f=this.optsEl.children;for(let m=0;m<f.length;m++){let p=f[m],g=r.options[m],b=m*this.optHeight+t,u="";if(s!==0){let k=b*s;Math.abs(k)<=90?(a=0,n=90,u=`rotateX(${k}deg) `):a=-9999}else n=0,a=b;let y=d===m;u+=`translate3d(0px,${a}px,${n}px) `,this.scaleFactor!==1&&!y&&(u+=h),this.noAnimate?(g.duration=0,p.style.transitionDuration=""):e!==g.duration&&(g.duration=e,p.style.transitionDuration=c),u!==g.transform&&(g.transform=u),p.style.transform=u,g.selected=y,y?p.classList.add(mt):p.classList.remove(mt)}this.col.prevSelected=l,i&&(this.y=t),this.lastIndex!==d&&(be(),this.lastIndex=d)}decelerate(){if(this.velocity!==0){this.velocity*=Bt,this.velocity=this.velocity>0?Math.max(this.velocity,1):Math.min(this.velocity,-1);let t=this.y+this.velocity;t>this.minY?(t=this.minY,this.velocity=0):t<this.maxY&&(t=this.maxY,this.velocity=0),this.update(t,0,!0),Math.round(t)%this.optHeight!==0||Math.abs(this.velocity)>1?this.rafId=requestAnimationFrame(()=>this.decelerate()):(this.velocity=0,this.emitColChange(),ye())}else if(this.y%this.optHeight!==0){let t=Math.abs(this.y%this.optHeight);this.velocity=t>this.optHeight/2?1:-1,this.decelerate()}}indexForY(t){return Math.min(Math.max(Math.abs(Math.round(t/this.optHeight)),0),this.col.options.length-1)}onStart(t){t.event.cancelable&&t.event.preventDefault(),t.event.stopPropagation(),fe(),this.rafId!==void 0&&cancelAnimationFrame(this.rafId);let e=this.col.options,i=e.length-1,a=0;for(let n=0;n<e.length;n++)e[n].disabled||(i=Math.min(i,n),a=Math.max(a,n));this.minY=-(i*this.optHeight),this.maxY=-(a*this.optHeight)}onMove(t){t.event.cancelable&&t.event.preventDefault(),t.event.stopPropagation();let e=this.y+t.deltaY;e>this.minY?(e=Math.pow(e,.8),this.bounceFrom=e):e<this.maxY?(e+=Math.pow(this.maxY-e,.9),this.bounceFrom=e):this.bounceFrom=0,this.update(e,0,!1)}onEnd(t){if(this.bounceFrom>0){this.update(this.minY,100,!0),this.emitColChange();return}else if(this.bounceFrom<0){this.update(this.maxY,100,!0),this.emitColChange();return}if(this.velocity=ee(-90,t.velocityY*23,Ht),this.velocity===0&&t.deltaY===0){let e=t.event.target.closest(".picker-opt");e?.hasAttribute("opt-index")&&this.setSelected(parseInt(e.getAttribute("opt-index"),10),ut)}else{if(this.y+=t.deltaY,Math.abs(t.velocityY)<.05){let e=t.deltaY>0,i=Math.abs(this.y)%this.optHeight/this.optHeight;e&&i>.5?this.velocity=Math.abs(this.velocity)*-1:!e&&i<=.5&&(this.velocity=Math.abs(this.velocity))}this.decelerate()}}refresh(t,e){var i;let a=this.col.options.length-1,n=0,r=this.col.options;for(let l=0;l<r.length;l++)r[l].disabled||(a=Math.min(a,l),n=Math.max(n,l));if(this.velocity!==0)return;let s=ee(a,(i=this.col.selectedIndex)!==null&&i!==void 0?i:0,n);if(this.col.prevSelected!==s||t){let l=s*this.optHeight*-1,d=e?ut:0;this.velocity=0,this.update(l,d,!0)}}onDomChange(t,e){let i=this.optsEl;i&&(this.optHeight=i.firstElementChild?i.firstElementChild.clientHeight:0),this.refresh(t,e)}render(){let t=this.col,e=F(this);return o(G,{key:"ed32d108dd94f0302fb453c31a3497ebae65ec37",class:Object.assign({[e]:!0,"picker-col":!0,"picker-opts-left":this.col.align==="left","picker-opts-right":this.col.align==="right"},_(t.cssClass)),style:{"max-width":this.col.columnWidth}},t.prefix&&o("div",{key:"9f0634890e66fd4ae74f826d1eea3431de121393",class:"picker-prefix",style:{width:t.prefixWidth}},t.prefix),o("div",{key:"337e996e5be91af16446085fe22436f213b771eb",class:"picker-opts",style:{maxWidth:t.optionsWidth},ref:i=>this.optsEl=i},t.options.map((i,a)=>o("button",{"aria-label":i.ariaLabel,class:{"picker-opt":!0,"picker-opt-disabled":!!i.disabled},"opt-index":a},i.text))),t.suffix&&o("div",{key:"d69a132599d78d9e5107f12228978cfce4e43098",class:"picker-suffix",style:{width:t.suffixWidth}},t.suffix))}get el(){return X(this)}static get watchers(){return{col:["colChanged"]}}},mt="picker-opt-selected",Bt=.97,Ht=90,ut=150;Rt.style={ios:Et,md:Yt};export{jt as ion_datetime,zt as ion_picker_legacy,Rt as ion_picker_legacy_column};
