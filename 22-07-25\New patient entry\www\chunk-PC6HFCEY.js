import{b as a}from"./chunk-BMJVVMK5.js";import{d as n}from"./chunk-XFXTD7QR.js";import{f as r,i as l,m as i,n as s,o as c}from"./chunk-EHNA26RN.js";import"./chunk-2R6CW7ES.js";var h="button{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;width:100%;height:34px;border:0px;outline:none;background:transparent;color:inherit;font-family:var(--ion-font-family, inherit);font-size:inherit;line-height:34px;text-align:inherit;text-overflow:ellipsis;white-space:nowrap;cursor:pointer;overflow:hidden}:host(.option-disabled){opacity:0.4}:host(.option-disabled) button{cursor:default}",b="button{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;width:100%;height:34px;border:0px;outline:none;background:transparent;color:inherit;font-family:var(--ion-font-family, inherit);font-size:inherit;line-height:34px;text-align:inherit;text-overflow:ellipsis;white-space:nowrap;cursor:pointer;overflow:hidden}:host(.option-disabled){opacity:0.4}:host(.option-disabled) button{cursor:default}:host(.option-active){color:var(--ion-color-base)}",g=(()=>{let o=class{constructor(t){l(this,t),this.pickerColumn=null,this.ariaLabel=null,this.disabled=!1,this.color="primary"}onAriaLabelChange(t){this.ariaLabel=t}componentWillLoad(){let t=n(this.el,["aria-label"]);this.ariaLabel=t["aria-label"]||null}connectedCallback(){this.pickerColumn=this.el.closest("ion-picker-column")}disconnectedCallback(){this.pickerColumn=null}componentDidLoad(){let{pickerColumn:t}=this;t!==null&&t.scrollActiveItemIntoView()}onClick(){let{pickerColumn:t}=this;t!==null&&t.setValue(this.value)}render(){let{color:t,disabled:e,ariaLabel:d}=this,p=r(this);return i(s,{key:"f816729941aabcb31ddfdce3ffe2e2139030d715",class:a(t,{[p]:!0,"option-disabled":e})},i("button",{key:"48dff7833bb60fc8331cd353a0885e6affa683d1",tabindex:"-1","aria-label":d,disabled:e,onClick:()=>this.onClick()},i("slot",{key:"f9224d0e7b7aa6c05b29abfdcfe0f30ad6ee3141"})))}get el(){return c(this)}static get watchers(){return{"aria-label":["onAriaLabelChange"]}}};return o.style={ios:h,md:b},o})();export{g as ion_picker_column_option};
