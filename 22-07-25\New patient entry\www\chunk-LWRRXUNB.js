import{a as w}from"./chunk-572LLHHX.js";import{f as x,j as y,k as v}from"./chunk-KRSA4YKC.js";import{b as u}from"./chunk-BMJVVMK5.js";import{p}from"./chunk-XFXTD7QR.js";import{b as k,f as m,i as c,m as o,n as h,o as f,p as g}from"./chunk-EHNA26RN.js";import{g as l}from"./chunk-2R6CW7ES.js";var j=":host{--background:var(--ion-color-primary, #0054e9);--color:var(--ion-color-primary-contrast, #fff);background:var(--background);color:var(--color);font-family:var(--ion-font-family, inherit)}:host(.ion-color){background:var(--ion-color-base);color:var(--ion-color-contrast)}.button-native{font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;-webkit-padding-start:0.7em;padding-inline-start:0.7em;-webkit-padding-end:0.7em;padding-inline-end:0.7em;padding-top:0;padding-bottom:0;display:inline-block;position:relative;width:100%;height:100%;border:0;outline:none;background:transparent;cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none;-webkit-box-sizing:border-box;box-sizing:border-box}.button-inner{display:-ms-flexbox;display:flex;-ms-flex-flow:column nowrap;flex-flow:column nowrap;-ms-flex-negative:0;flex-shrink:0;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%}.horizontal-wrapper{display:-ms-flexbox;display:flex;-ms-flex-flow:row nowrap;flex-flow:row nowrap;-ms-flex-negative:0;flex-shrink:0;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%}::slotted(*){-ms-flex-negative:0;flex-shrink:0}::slotted([slot=start]){-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:5px;margin-inline-end:5px;margin-top:0;margin-bottom:0}::slotted([slot=end]){-webkit-margin-start:5px;margin-inline-start:5px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0}::slotted([slot=icon-only]){padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;-webkit-margin-start:10px;margin-inline-start:10px;-webkit-margin-end:10px;margin-inline-end:10px;margin-top:0;margin-bottom:0;min-width:0.9em;font-size:1.8em}:host(.item-option-expandable){-ms-flex-negative:0;flex-shrink:0;-webkit-transition-duration:0;transition-duration:0;-webkit-transition-property:none;transition-property:none;-webkit-transition-timing-function:cubic-bezier(0.65, 0.05, 0.36, 1);transition-timing-function:cubic-bezier(0.65, 0.05, 0.36, 1)}:host(.item-option-disabled){pointer-events:none}:host(.item-option-disabled) .button-native{cursor:default;opacity:0.5;pointer-events:none}:host{font-size:clamp(16px, 1rem, 35.2px)}:host(.ion-activated){background:var(--ion-color-primary-shade, #004acd)}:host(.ion-color.ion-activated){background:var(--ion-color-shade)}",W=":host{--background:var(--ion-color-primary, #0054e9);--color:var(--ion-color-primary-contrast, #fff);background:var(--background);color:var(--color);font-family:var(--ion-font-family, inherit)}:host(.ion-color){background:var(--ion-color-base);color:var(--ion-color-contrast)}.button-native{font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;-webkit-padding-start:0.7em;padding-inline-start:0.7em;-webkit-padding-end:0.7em;padding-inline-end:0.7em;padding-top:0;padding-bottom:0;display:inline-block;position:relative;width:100%;height:100%;border:0;outline:none;background:transparent;cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none;-webkit-box-sizing:border-box;box-sizing:border-box}.button-inner{display:-ms-flexbox;display:flex;-ms-flex-flow:column nowrap;flex-flow:column nowrap;-ms-flex-negative:0;flex-shrink:0;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%}.horizontal-wrapper{display:-ms-flexbox;display:flex;-ms-flex-flow:row nowrap;flex-flow:row nowrap;-ms-flex-negative:0;flex-shrink:0;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%}::slotted(*){-ms-flex-negative:0;flex-shrink:0}::slotted([slot=start]){-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:5px;margin-inline-end:5px;margin-top:0;margin-bottom:0}::slotted([slot=end]){-webkit-margin-start:5px;margin-inline-start:5px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0}::slotted([slot=icon-only]){padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;-webkit-margin-start:10px;margin-inline-start:10px;-webkit-margin-end:10px;margin-inline-end:10px;margin-top:0;margin-bottom:0;min-width:0.9em;font-size:1.8em}:host(.item-option-expandable){-ms-flex-negative:0;flex-shrink:0;-webkit-transition-duration:0;transition-duration:0;-webkit-transition-property:none;transition-property:none;-webkit-transition-timing-function:cubic-bezier(0.65, 0.05, 0.36, 1);transition-timing-function:cubic-bezier(0.65, 0.05, 0.36, 1)}:host(.item-option-disabled){pointer-events:none}:host(.item-option-disabled) .button-native{cursor:default;opacity:0.5;pointer-events:none}:host{font-size:0.875rem;font-weight:500;text-transform:uppercase}",q=(()=>{let t=class{constructor(i){c(this,i),this.disabled=!1,this.expandable=!1,this.type="button",this.onClick=e=>{e.target.closest("ion-item-option")&&e.preventDefault()}}render(){let{disabled:i,expandable:e,href:n}=this,s=n===void 0?"button":"a",r=m(this),d=s==="button"?{type:this.type}:{download:this.download,href:this.href,target:this.target};return o(h,{key:"189a0040b97163b2336bf216baa71d584c5923a8",onClick:this.onClick,class:u(this.color,{[r]:!0,"item-option-disabled":i,"item-option-expandable":e,"ion-activatable":!0})},o(s,Object.assign({key:"5a7140eb99da5ec82fe2ea3ea134513130763399"},d,{class:"button-native",part:"native",disabled:i}),o("span",{key:"9b8577e612706b43e575c9a20f2f9d35c0d1bcb1",class:"button-inner"},o("slot",{key:"9acb82f04e4822bfaa363cc2c4d29d5c0fec0ad6",name:"top"}),o("div",{key:"66f5fb4fdd0c39f205574c602c793dcf109c7a17",class:"horizontal-wrapper"},o("slot",{key:"3761a32bca7c6c41b7eb394045497cfde181a62a",name:"start"}),o("slot",{key:"a96a568955cf6962883dc6771726d3d07462da00",name:"icon-only"}),o("slot",{key:"af5dfe5eb41456b9359bafe3615b576617ed7b57"}),o("slot",{key:"00426958066ab7b949ff966fabad5cf8a0b54079",name:"end"})),o("slot",{key:"ae66c8bd536a9f27865f49240980d7b4b831b229",name:"bottom"})),r==="md"&&o("ion-ripple-effect",{key:"30df6c935ef8a3f28a6bc1f3bb162ca4f80aaf26"})))}get el(){return f(this)}};return t.style={ios:j,md:W},t})(),C="ion-item-options{top:0;right:0;-ms-flex-pack:end;justify-content:flex-end;display:none;position:absolute;height:100%;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:1}:host-context([dir=rtl]) ion-item-options{-ms-flex-pack:start;justify-content:flex-start}:host-context([dir=rtl]) ion-item-options:not(.item-options-end){right:auto;left:0;-ms-flex-pack:end;justify-content:flex-end}[dir=rtl] ion-item-options{-ms-flex-pack:start;justify-content:flex-start}[dir=rtl] ion-item-options:not(.item-options-end){right:auto;left:0;-ms-flex-pack:end;justify-content:flex-end}@supports selector(:dir(rtl)){ion-item-options:dir(rtl){-ms-flex-pack:start;justify-content:flex-start}ion-item-options:dir(rtl):not(.item-options-end){right:auto;left:0;-ms-flex-pack:end;justify-content:flex-end}}.item-options-start{right:auto;left:0;-ms-flex-pack:start;justify-content:flex-start}:host-context([dir=rtl]) .item-options-start{-ms-flex-pack:end;justify-content:flex-end}[dir=rtl] .item-options-start{-ms-flex-pack:end;justify-content:flex-end}@supports selector(:dir(rtl)){.item-options-start:dir(rtl){-ms-flex-pack:end;justify-content:flex-end}}[dir=ltr] .item-options-start ion-item-option:first-child,[dir=rtl] .item-options-start ion-item-option:last-child{padding-left:var(--ion-safe-area-left)}[dir=ltr] .item-options-end ion-item-option:last-child,[dir=rtl] .item-options-end ion-item-option:first-child{padding-right:var(--ion-safe-area-right)}:host-context([dir=rtl]) .item-sliding-active-slide.item-sliding-active-options-start ion-item-options:not(.item-options-end){width:100%;visibility:visible}[dir=rtl] .item-sliding-active-slide.item-sliding-active-options-start ion-item-options:not(.item-options-end){width:100%;visibility:visible}@supports selector(:dir(rtl)){.item-sliding-active-slide:dir(rtl).item-sliding-active-options-start ion-item-options:not(.item-options-end){width:100%;visibility:visible}}.item-sliding-active-slide ion-item-options{display:-ms-flexbox;display:flex;visibility:hidden}.item-sliding-active-slide.item-sliding-active-options-start .item-options-start,.item-sliding-active-slide.item-sliding-active-options-end ion-item-options:not(.item-options-start){width:100%;visibility:visible}.item-options-ios{border-bottom-width:0;border-bottom-style:solid;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, var(--ion-background-color-step-250, #c8c7cc))))}.item-options-ios.item-options-end{border-bottom-width:0.55px}.list-ios-lines-none .item-options-ios{border-bottom-width:0}.list-ios-lines-full .item-options-ios,.list-ios-lines-inset .item-options-ios.item-options-end{border-bottom-width:0.55px}",A="ion-item-options{top:0;right:0;-ms-flex-pack:end;justify-content:flex-end;display:none;position:absolute;height:100%;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:1}:host-context([dir=rtl]) ion-item-options{-ms-flex-pack:start;justify-content:flex-start}:host-context([dir=rtl]) ion-item-options:not(.item-options-end){right:auto;left:0;-ms-flex-pack:end;justify-content:flex-end}[dir=rtl] ion-item-options{-ms-flex-pack:start;justify-content:flex-start}[dir=rtl] ion-item-options:not(.item-options-end){right:auto;left:0;-ms-flex-pack:end;justify-content:flex-end}@supports selector(:dir(rtl)){ion-item-options:dir(rtl){-ms-flex-pack:start;justify-content:flex-start}ion-item-options:dir(rtl):not(.item-options-end){right:auto;left:0;-ms-flex-pack:end;justify-content:flex-end}}.item-options-start{right:auto;left:0;-ms-flex-pack:start;justify-content:flex-start}:host-context([dir=rtl]) .item-options-start{-ms-flex-pack:end;justify-content:flex-end}[dir=rtl] .item-options-start{-ms-flex-pack:end;justify-content:flex-end}@supports selector(:dir(rtl)){.item-options-start:dir(rtl){-ms-flex-pack:end;justify-content:flex-end}}[dir=ltr] .item-options-start ion-item-option:first-child,[dir=rtl] .item-options-start ion-item-option:last-child{padding-left:var(--ion-safe-area-left)}[dir=ltr] .item-options-end ion-item-option:last-child,[dir=rtl] .item-options-end ion-item-option:first-child{padding-right:var(--ion-safe-area-right)}:host-context([dir=rtl]) .item-sliding-active-slide.item-sliding-active-options-start ion-item-options:not(.item-options-end){width:100%;visibility:visible}[dir=rtl] .item-sliding-active-slide.item-sliding-active-options-start ion-item-options:not(.item-options-end){width:100%;visibility:visible}@supports selector(:dir(rtl)){.item-sliding-active-slide:dir(rtl).item-sliding-active-options-start ion-item-options:not(.item-options-end){width:100%;visibility:visible}}.item-sliding-active-slide ion-item-options{display:-ms-flexbox;display:flex;visibility:hidden}.item-sliding-active-slide.item-sliding-active-options-start .item-options-start,.item-sliding-active-slide.item-sliding-active-options-end ion-item-options:not(.item-options-start){width:100%;visibility:visible}.item-options-md{border-bottom-width:0;border-bottom-style:solid;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-150, var(--ion-background-color-step-150, rgba(0, 0, 0, 0.13)))))}.list-md-lines-none .item-options-md{border-bottom-width:0}.list-md-lines-full .item-options-md,.list-md-lines-inset .item-options-md.item-options-end{border-bottom-width:1px}",P=(()=>{let t=class{constructor(i){c(this,i),this.ionSwipe=g(this,"ionSwipe",7),this.side="end"}fireSwipeEvent(){return l(this,null,function*(){this.ionSwipe.emit({side:this.side})})}render(){let i=m(this),e=p(this.side);return o(h,{key:"05a22a505e043c2715e3805e5e26ab4668940af0",class:{[i]:!0,[`item-options-${i}`]:!0,"item-options-start":!e,"item-options-end":e}})}get el(){return f(this)}};return t.style={ios:C,md:A},t})(),E="ion-item-sliding{display:block;position:relative;width:100%;overflow:hidden;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}ion-item-sliding .item{-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.item-sliding-active-slide .item{position:relative;-webkit-transition:-webkit-transform 500ms cubic-bezier(0.36, 0.66, 0.04, 1);transition:-webkit-transform 500ms cubic-bezier(0.36, 0.66, 0.04, 1);transition:transform 500ms cubic-bezier(0.36, 0.66, 0.04, 1);transition:transform 500ms cubic-bezier(0.36, 0.66, 0.04, 1), -webkit-transform 500ms cubic-bezier(0.36, 0.66, 0.04, 1);opacity:1;z-index:2;pointer-events:none;will-change:transform}.item-sliding-closing ion-item-options{pointer-events:none}.item-sliding-active-swipe-end .item-options-end .item-option-expandable{padding-left:100%;-ms-flex-order:1;order:1;-webkit-transition-duration:0.6s;transition-duration:0.6s;-webkit-transition-property:padding-left;transition-property:padding-left}:host-context([dir=rtl]) .item-sliding-active-swipe-end .item-options-end .item-option-expandable{-ms-flex-order:-1;order:-1}[dir=rtl] .item-sliding-active-swipe-end .item-options-end .item-option-expandable{-ms-flex-order:-1;order:-1}@supports selector(:dir(rtl)){.item-sliding-active-swipe-end .item-options-end .item-option-expandable:dir(rtl){-ms-flex-order:-1;order:-1}}.item-sliding-active-swipe-start .item-options-start .item-option-expandable{padding-right:100%;-ms-flex-order:-1;order:-1;-webkit-transition-duration:0.6s;transition-duration:0.6s;-webkit-transition-property:padding-right;transition-property:padding-right}:host-context([dir=rtl]) .item-sliding-active-swipe-start .item-options-start .item-option-expandable{-ms-flex-order:1;order:1}[dir=rtl] .item-sliding-active-swipe-start .item-options-start .item-option-expandable{-ms-flex-order:1;order:1}@supports selector(:dir(rtl)){.item-sliding-active-swipe-start .item-options-start .item-option-expandable:dir(rtl){-ms-flex-order:1;order:1}}",O=30,S=.55,a,R=class{constructor(t){c(this,t),this.ionDrag=g(this,"ionDrag",7),this.item=null,this.openAmount=0,this.initialOpenAmount=0,this.optsWidthRightSide=0,this.optsWidthLeftSide=0,this.sides=0,this.optsDirty=!0,this.contentEl=null,this.initialContentScrollY=!0,this.state=2,this.disabled=!1}disabledChanged(){this.gesture&&this.gesture.enable(!this.disabled)}connectedCallback(){return l(this,null,function*(){let{el:t}=this;this.item=t.querySelector("ion-item"),this.contentEl=x(t),this.mutationObserver=w(t,"ion-item-option",()=>l(this,null,function*(){yield this.updateOptions()})),yield this.updateOptions(),this.gesture=(yield import("./chunk-Z4ZXIL76.js")).createGesture({el:t,gestureName:"item-swipe",gesturePriority:100,threshold:5,canStart:i=>this.canStart(i),onStart:()=>this.onStart(),onMove:i=>this.onMove(i),onEnd:i=>this.onEnd(i)}),this.disabledChanged()})}disconnectedCallback(){this.gesture&&(this.gesture.destroy(),this.gesture=void 0),this.item=null,this.leftOptions=this.rightOptions=void 0,a===this.el&&(a=void 0),this.mutationObserver&&(this.mutationObserver.disconnect(),this.mutationObserver=void 0)}getOpenAmount(){return Promise.resolve(this.openAmount)}getSlidingRatio(){return Promise.resolve(this.getSlidingRatioSync())}open(t){return l(this,null,function*(){var i;if((this.item=(i=this.item)!==null&&i!==void 0?i:this.el.querySelector("ion-item"))===null)return;let n=this.getOptions(t);if(!n)return;t===void 0&&(t=n===this.leftOptions?"start":"end"),t=p(t)?"end":"start";let s=this.openAmount<0,r=this.openAmount>0;s&&n===this.leftOptions||r&&n===this.rightOptions||(this.closeOpened(),this.state=4,requestAnimationFrame(()=>{this.calculateOptsWidth();let d=t==="end"?this.optsWidthRightSide:-this.optsWidthLeftSide;a=this.el,this.setOpenAmount(d,!1),this.state=t==="end"?8:16}))})}close(){return l(this,null,function*(){this.setOpenAmount(0,!0)})}closeOpened(){return l(this,null,function*(){return a!==void 0?(a.close(),a=void 0,!0):!1})}getOptions(t){return t===void 0?this.leftOptions||this.rightOptions:t==="start"?this.leftOptions:this.rightOptions}updateOptions(){return l(this,null,function*(){var t;let i=this.el.querySelectorAll("ion-item-options"),e=0;this.leftOptions=this.rightOptions=void 0;for(let n=0;n<i.length;n++){let s=i.item(n),r=s.componentOnReady!==void 0?yield s.componentOnReady():s;(p((t=r.side)!==null&&t!==void 0?t:r.getAttribute("side"))?"end":"start")==="start"?(this.leftOptions=r,e|=1):(this.rightOptions=r,e|=2)}this.optsDirty=!0,this.sides=e})}canStart(t){if(document.dir==="rtl"?window.innerWidth-t.startX<15:t.startX<15)return!1;let n=a;return n&&n!==this.el&&this.closeOpened(),!!(this.rightOptions||this.leftOptions)}onStart(){this.item=this.el.querySelector("ion-item");let{contentEl:t}=this;t&&(this.initialContentScrollY=y(t)),a=this.el,this.tmr!==void 0&&(clearTimeout(this.tmr),this.tmr=void 0),this.openAmount===0&&(this.optsDirty=!0,this.state=4),this.initialOpenAmount=this.openAmount,this.item&&(this.item.style.transition="none")}onMove(t){this.optsDirty&&this.calculateOptsWidth();let i=this.initialOpenAmount-t.deltaX;switch(this.sides){case 2:i=Math.max(0,i);break;case 1:i=Math.min(0,i);break;case 3:break;case 0:return;default:k("[ion-item-sliding] - invalid ItemSideFlags value",this.sides);break}let e;i>this.optsWidthRightSide?(e=this.optsWidthRightSide,i=e+(i-e)*S):i<-this.optsWidthLeftSide&&(e=-this.optsWidthLeftSide,i=e+(i-e)*S),this.setOpenAmount(i,!1)}onEnd(t){let{contentEl:i,initialContentScrollY:e}=this;i&&v(i,e);let n=t.velocityX,s=this.openAmount>0?this.optsWidthRightSide:-this.optsWidthLeftSide,r=this.openAmount>0==!(n<0),d=Math.abs(n)>.3,z=Math.abs(this.openAmount)<Math.abs(s/2);I(r,d,z)&&(s=0);let b=this.state;this.setOpenAmount(s,!0),(b&32)!==0&&this.rightOptions?this.rightOptions.fireSwipeEvent():(b&64)!==0&&this.leftOptions&&this.leftOptions.fireSwipeEvent()}calculateOptsWidth(){this.optsWidthRightSide=0,this.rightOptions&&(this.rightOptions.style.display="flex",this.optsWidthRightSide=this.rightOptions.offsetWidth,this.rightOptions.style.display=""),this.optsWidthLeftSide=0,this.leftOptions&&(this.leftOptions.style.display="flex",this.optsWidthLeftSide=this.leftOptions.offsetWidth,this.leftOptions.style.display=""),this.optsDirty=!1}setOpenAmount(t,i){if(this.tmr!==void 0&&(clearTimeout(this.tmr),this.tmr=void 0),!this.item)return;let{el:e}=this,n=this.item.style;if(this.openAmount=t,i&&(n.transition=""),t>0)this.state=t>=this.optsWidthRightSide+O?40:8;else if(t<0)this.state=t<=-this.optsWidthLeftSide-O?80:16;else{e.classList.add("item-sliding-closing"),this.gesture&&this.gesture.enable(!1),this.tmr=setTimeout(()=>{this.state=2,this.tmr=void 0,this.gesture&&this.gesture.enable(!this.disabled),e.classList.remove("item-sliding-closing")},600),a=void 0,n.transform="";return}n.transform=`translate3d(${-t}px,0,0)`,this.ionDrag.emit({amount:t,ratio:this.getSlidingRatioSync()})}getSlidingRatioSync(){return this.openAmount>0?this.openAmount/this.optsWidthRightSide:this.openAmount<0?this.openAmount/this.optsWidthLeftSide:0}render(){let t=m(this);return o(h,{key:"d812322c9fb5da4ee16e99dc38bfb24cb4590d03",class:{[t]:!0,"item-sliding-active-slide":this.state!==2,"item-sliding-active-options-end":(this.state&8)!==0,"item-sliding-active-options-start":(this.state&16)!==0,"item-sliding-active-swipe-end":(this.state&32)!==0,"item-sliding-active-swipe-start":(this.state&64)!==0}})}get el(){return f(this)}static get watchers(){return{disabled:["disabledChanged"]}}},I=(t,i,e)=>!i&&e||t&&i;R.style=E;export{q as ion_item_option,P as ion_item_options,R as ion_item_sliding};
